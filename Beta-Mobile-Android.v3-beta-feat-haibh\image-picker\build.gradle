apply plugin: 'com.android.library'
apply plugin: 'kotlin-android-extensions'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

buildscript {
    ext.kotlin_version = '1.6.21'
    repositories {
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"

        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

android {
    compileSdkVersion rootProject.ext.compileSdkVersion

    defaultConfig {
        minSdkVersion rootProject.ext.minSdkVersion
        targetSdkVersion rootProject.ext.targetSdkVersion
        versionCode 1
        versionName "1.0"
    }

    lintOptions {
        abortOnError false
    }

    buildTypes {
        dev {
            debuggable true
        }
        customer {
            debuggable true
        }

        tprod {
            debuggable true
        }
        prod {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            debuggable false
        }
    }
}

repositories {
    maven { url "https://maven.google.com" }
    jcenter()
}

dependencies {
    implementation libraries.supportAppCompat
    implementation libraries.supportExifInterface

    implementation libraries.rxJava
    implementation libraries.rxAndroid
    implementation libraries.rxKotlin
    implementation 'com.theartofdev.edmodo:android-image-cropper:2.8.0'
    implementation "androidx.core:core-ktx:1.6.0"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
