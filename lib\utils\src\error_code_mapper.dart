import 'package:easy_localization/easy_localization.dart';

/// Extension to map API error codes to localized messages
/// Similar to Android String.mapCode() extension
extension ErrorCodeMapper on String {
  /// Map error code to localized message
  /// Returns the localized message if found, otherwise returns the original string
  String mapErrorCode() {
    final upperCode = toUpperCase();

    // Check if localization key exists
    final localizationKey = 'ApiErrors.$upperCode';
    final localizedMessage = localizationKey.tr();

    // If translation exists and is different from the key, return it
    if (localizedMessage != localizationKey) {
      return localizedMessage;
    }

    // Fallback to original message if no translation found
    return this;
  }

  /// Check if the error code has a localized message
  bool hasLocalizedErrorMessage() {
    final upperCode = toUpperCase();
    final localizationKey = 'ApiErrors.$upperCode';
    final localizedMessage = localizationKey.tr();

    return localizedMessage != localizationKey;
  }
}

/// Utility class for handling API error responses
class ApiErrorHandler {
  /// Handle API error response and return appropriate message
  /// [showOriginalIfNotMapped] - if true, shows original message when no mapping found
  /// [forceMapping] - if true, always tries to map even if message seems user-friendly

  static String handleError(
  String? errorMessage,  {
  String? fallbackMessage,
  bool showOriginalIfNotMapped = true,
  bool forceMapping = false,
  }) {

    // Nếu errorMessage null hoặc rỗng → dùng fallbackMessage hoặc chuỗi rỗng
    if (errorMessage == null || errorMessage.isEmpty) {
      return fallbackMessage ?? '';
    }

    // Thử ánh xạ qua mapErrorCode (nếu có ánh xạ thì trả về kết quả)
    final mappedMessage = errorMessage.mapErrorCode();

    return mappedMessage;
  }

  // static String handleError(
  //   String? errorMessage,  {
  //       int status = 0 ,
  //   String? fallbackMessage,
  //   bool showOriginalIfNotMapped = true,
  //   bool forceMapping = false,
  // }) {
  //   if (status != 1 && (errorMessage == null || errorMessage.isEmpty)) {
  //     return fallbackMessage ?? '';
  //   }
  //
  //   // Try to map the error code to localized message
  //   final mappedMessage = errorMessage?.mapErrorCode();
  //
  //   // If mapping was successful (message changed), return it
  //   if (mappedMessage != errorMessage) {
  //     return mappedMessage ?? '';
  //   }
  //
  //   // If no mapping found, decide what to show based on configuration
  //   if (showOriginalIfNotMapped) {
  //     return  errorMessage ?? '';
  //   } else {
  //     return fallbackMessage ?? '';
  //   }
  // }

  /// Handle success message
  static String handleSuccess(String? successMessage) {
    if (successMessage == null || successMessage.isEmpty) {
      return 'ApiErrors.UNKNOWN_ERROR'.tr();
    }

    return successMessage.mapErrorCode();
  }

  /// Check if message is a success message
  static bool isSuccessMessage(String? message) {
    if (message == null || message.isEmpty) return false;

    final upperMessage = message.toUpperCase();
    return upperMessage.contains('SUCCESS') ||
           upperMessage.contains('SUCCESSFULLY') ||
           upperMessage == 'LOGIN_SUCCESSFULLY' ||
           upperMessage == 'REGISTRATION_SUCCESSFULLY' ||
           upperMessage == 'REGISTRATION_SUCCESS' ||
           upperMessage == 'UPDATE_SUCCESSFULLY' ||
           upperMessage == 'PASSWORD_CHANGED_SUCCESSFULLY';
  }

  /// Handle error message for specific feature/function
  /// This allows different handling strategies per feature
  static String handleErrorForFeature(
    String? errorMessage,
    String feature, {
    String? fallbackMessage,
  }) {
    if (errorMessage == null || errorMessage.isEmpty) {
      return fallbackMessage ?? 'ApiErrors.UNKNOWN_ERROR'.tr();
    }

    // Define feature-specific behavior
    switch (feature.toLowerCase()) {
      case 'login':
      case 'register':
      case 'auth':
        // Always try to map auth-related errors
        return handleError(errorMessage,
          fallbackMessage: fallbackMessage,
          showOriginalIfNotMapped: false, // Don't show raw error codes
          forceMapping: true
        );

      case 'profile':
      case 'update':
        // Show original message if mapping not found (might be user-friendly)
        return handleError(errorMessage,
          fallbackMessage: fallbackMessage,
          showOriginalIfNotMapped: true
        );

      case 'payment':
      case 'booking':
        // Critical features - always provide user-friendly messages
        return handleError(errorMessage,
          fallbackMessage: fallbackMessage ?? 'Có lỗi xảy ra, vui lòng thử lại',
          showOriginalIfNotMapped: false
        );

      default:
        // Default behavior
        return handleError(errorMessage, fallbackMessage: fallbackMessage);
    }
  }

  /// Get appropriate message type for UI display
  static MessageType getMessageType(String? message) {
    if (message == null || message.isEmpty) return MessageType.error;

    if (isSuccessMessage(message)) {
      return MessageType.success;
    }

    final upperMessage = message.toUpperCase();
    if (upperMessage.contains('ERROR') ||
        upperMessage.contains('FAIL') ||
        upperMessage.contains('INCORRECT') ||
        upperMessage.contains('EXISTED') ||
        upperMessage.contains('LOCKED') ||
        upperMessage.contains('NOT_EXISTED')) {
      return MessageType.error;
    }

    return MessageType.info;
  }

  /// Debug method to track unmapped error codes
  /// Useful for development to identify missing translations
  static void logUnmappedError(String errorCode, String context) {
    if (!errorCode.hasLocalizedErrorMessage()) {
      print('🚨 UNMAPPED ERROR CODE: "$errorCode" in context: $context');
      print('💡 Add to translation files: "ApiErrors.$errorCode": "Your localized message"');
    }
  }

  /// Get all error codes that need translation
  /// Useful for generating translation templates
  static Set<String> _unmappedCodes = <String>{};

  static void trackUnmappedCode(String errorCode) {
    if (!errorCode.hasLocalizedErrorMessage()) {
      _unmappedCodes.add(errorCode.toUpperCase());
    }
  }

  static Set<String> getUnmappedCodes() => Set.from(_unmappedCodes);

  static void clearUnmappedCodes() => _unmappedCodes.clear();
}

/// Message type for UI display
enum MessageType {
  success,
  error,
  warning,
  info
}

/// Extension for MessageType to get appropriate colors/icons
extension MessageTypeExtension on MessageType {
  /// Get color for message type
  String get colorName {
    switch (this) {
      case MessageType.success:
        return 'green';
      case MessageType.error:
        return 'red';
      case MessageType.warning:
        return 'orange';
      case MessageType.info:
        return 'blue';
    }
  }

  /// Get icon for message type
  String get iconName {
    switch (this) {
      case MessageType.success:
        return 'check_circle';
      case MessageType.error:
        return 'error';
      case MessageType.warning:
        return 'warning';
      case MessageType.info:
        return 'info';
    }
  }
}
