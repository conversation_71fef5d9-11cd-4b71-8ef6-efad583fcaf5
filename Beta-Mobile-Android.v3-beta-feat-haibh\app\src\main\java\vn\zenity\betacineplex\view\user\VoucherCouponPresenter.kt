package vn.zenity.betacineplex.view.user

import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class VoucherCouponPresenter : VoucherCouponContractor.Presenter {
    private var view: WeakReference<VoucherCouponContractor.View?>? = null
    override fun attachView(view: VoucherCouponContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
