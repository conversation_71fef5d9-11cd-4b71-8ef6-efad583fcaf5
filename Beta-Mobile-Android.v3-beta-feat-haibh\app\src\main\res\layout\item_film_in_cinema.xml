<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent" android:layout_height="wrap_content"
    android:background="?attr/selectableItemBackground"
    android:paddingLeft="8dp"
    android:paddingRight="8dp"
    android:layout_marginTop="20dp"
    android:paddingTop="3dp">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardBG"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="@dimen/padding_small"
        android:scaleType="fitStart"
        app:cardCornerRadius="0dp"
        app:cardElevation="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/card"
        android:layout_width="105dp"
        android:layout_height="166dp"
        android:layout_marginLeft="@dimen/padding_small"
        app:cardCornerRadius="@dimen/small_radius"
        android:layout_marginBottom="10dp"
        app:cardElevation="1dp"
        android:layout_margin="1dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivBanner"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/grayBg"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivIsHot"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:scaleType="fitXY"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_gravity="right"
            app:srcCompat="@drawable/ic_hot"/>
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIsHot2"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:scaleType="fitXY"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintRight_toLeftOf="@+id/tvFilmTitle"
        android:layout_marginRight="25dp"
        app:layout_constraintTop_toTopOf="parent"
        android:elevation="1dp"
        app:srcCompat="@drawable/ic_hot"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/btnPlayTrailer"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:elevation="1dp"
        app:layout_constraintBottom_toBottomOf="@+id/card"
        app:layout_constraintLeft_toLeftOf="@+id/card"
        app:layout_constraintRight_toRightOf="@+id/card"
        app:layout_constraintTop_toTopOf="@+id/card"
        app:srcCompat="@drawable/opacity"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilmTitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="26dp"
        android:layout_marginTop="@dimen/padding_small"
        android:ellipsize="end"
        tools:text="Pacific Rim: Trỗi Dậy"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/oswald_bold"
        android:textColor="@color/textDark"
        app:layout_constraintLeft_toRightOf="@+id/card"
        app:layout_constraintRight_toLeftOf="@+id/fillSTT"
        app:layout_constraintTop_toTopOf="@+id/cardBG"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/fillSTT"
        android:layout_width="30dp"
        android:layout_height="wrap_content"
        android:layout_marginRight="@dimen/margin_small"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@+id/cardBG"
        app:layout_constraintTop_toTopOf="@+id/cardBG"
        app:srcCompat="@drawable/fill"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilm2d"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        tools:text="(2D - LT)"
        android:textColor="@color/textDark"
        android:textSize="@dimen/font_normal"
        android:visibility="gone"
        app:fontFamily="@font/oswald_regular"
        app:layout_constraintLeft_toLeftOf="@+id/tvFilmTitle"
        app:layout_constraintTop_toBottomOf="@+id/tvFilmTitle"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivTypeAge"
        android:layout_width="36dp"
        android:layout_height="18dp"
        android:layout_marginStart="5dp"
        android:layout_marginTop="5dp"
        android:elevation="1dp"
        app:layout_constraintLeft_toRightOf="@+id/tvFilm2d"
        app:layout_constraintStart_toStartOf="@+id/card"
        app:layout_constraintTop_toTopOf="@+id/card"
        app:srcCompat="@drawable/ic_age_c_13" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilmType"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        android:singleLine="true"
        android:maxLines="1"
        tools:text="Võ thuật, Viễn Tưởng"
        android:textSize="@dimen/font_normal"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/sanspro_regular"
        app:layout_constraintLeft_toLeftOf="@+id/tvFilmTitle"
        app:layout_constraintRight_toRightOf="@+id/cardBG"
        app:layout_constraintTop_toBottomOf="@+id/tvFilm2d"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFilmDuration"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:ellipsize="end"
        tools:text="135 phút"
        android:textSize="@dimen/font_normal"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/sanspro_regular"
        app:layout_constraintLeft_toLeftOf="@+id/tvFilmTitle"
        app:layout_constraintRight_toRightOf="@+id/cardBG"
        app:layout_constraintTop_toBottomOf="@+id/tvFilmType"/>

</androidx.constraintlayout.widget.ConstraintLayout>