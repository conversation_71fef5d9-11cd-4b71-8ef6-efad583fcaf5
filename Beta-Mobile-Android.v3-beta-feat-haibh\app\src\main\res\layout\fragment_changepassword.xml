<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/change_password"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCurrentPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp"
        android:paddingLeft="24dp"
        android:paddingRight="24dp"
        android:paddingBottom="@dimen/padding_normal"
        android:text="@string/enter_current_password"/>

    <vn.zenity.betacineplex.helper.view.BetaEditText
        android:id="@+id/edtOldPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/margin_large"
        android:paddingEnd="@dimen/margin_large"
        android:inputType="textPassword"
        app:btedIcon="@drawable/password"
        app:btedHint="@string/current_password"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNewPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="24dp"
        android:paddingLeft="24dp"
        android:paddingRight="24dp"
        android:paddingBottom="@dimen/padding_normal"
        android:text="@string/enter_new_password"/>

    <vn.zenity.betacineplex.helper.view.BetaEditText
        android:id="@+id/edtNewPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/margin_large"
        android:paddingEnd="@dimen/margin_large"
        android:inputType="textPassword"
        app:btedIcon="@drawable/password"
        app:btedHint="@string/new_password"/>

    <vn.zenity.betacineplex.helper.view.BetaEditText
        android:id="@+id/edtConfirmPassword"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/margin_normal"
        android:paddingStart="@dimen/margin_large"
        android:paddingEnd="@dimen/margin_large"
        android:inputType="textPassword"
        app:btedIcon="@drawable/comfirm_password"
        app:btedHint="@string/confirm_new_password"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnConfirm"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_large"
        android:text="@string/update"
        style="@style/ButtonOrange"/>

</LinearLayout>
