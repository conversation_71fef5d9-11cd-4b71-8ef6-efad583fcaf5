<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar" />

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@color/grayfdfdfd"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/btnLogin"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginLeft="8dp"
            android:layout_marginRight="8dp"
            android:background="?attr/selectableItemBackground"
            android:visibility="gone">

            <View
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/border_blue_radius"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivIconAvatar"
                android:layout_width="16dp"
                android:layout_height="16dp"
                android:layout_marginLeft="11dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_member_login" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvLogin"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="8dp"
                android:layout_marginRight="15dp"
                android:gravity="center"
                android:text="@string/login"
                android:textColor="@color/colorPrimary"
                android:textSize="14sp"
                app:fontFamily="@font/oswald_medium"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toRightOf="@+id/ivIconAvatar"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivAvatar"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="7dp"
            app:srcCompat="@drawable/ic_account" />

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvUsername"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="@color/textBlack"
                android:textSize="@dimen/font_normal"
                android:visibility="gone"
                app:fontFamily="@font/sanspro_regular"
                tools:text="Hi, Đỗ Ngọc Vinh"
                tools:visibility="visible" />

            <LinearLayout
                android:id="@+id/llUserInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivUserType"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    app:srcCompat="@drawable/ic_membership" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvUserType"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:singleLine="true"
                    android:textSize="@dimen/font_small"
                    app:fontFamily="@font/sanspro_semi_bold"
                    tools:text="Standard" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivNumberStar"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="10dp"
                    app:srcCompat="@drawable/ic_point" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNumberStar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textSize="@dimen/font_small"
                    app:fontFamily="@font/sanspro_semi_bold"
                    tools:text="50" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/ivNumberVoucher"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:layout_marginLeft="10dp"
                    app:srcCompat="@drawable/ic_voucher" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNumberVoucher"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="5dp"
                    android:layout_marginRight="5dp"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textSize="@dimen/font_small"
                    app:fontFamily="@font/sanspro_semi_bold"
                    tools:text="450" />

            </LinearLayout>
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_menubuger_white" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivLogo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:padding="7dp"
            app:srcCompat="@drawable/ic_beta_top_home" />
    </LinearLayout>

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/grayLine" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="55dp"
        android:background="@color/grayfdfdfd"
        android:elevation="2dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tab1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="?attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:text="@string/coming_soon"
            android:textColor="@color/color_tab_selector"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_bold"
            app:textAllCaps="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tab2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:text="@string/now_showing"
            android:textColor="@color/color_tab_selector"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_bold"
            app:textAllCaps="true" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tab3"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="?attr/selectableItemBackground"
            android:gravity="center"
            android:paddingLeft="8dp"
            android:paddingRight="8dp"
            android:text="@string/special_show"
            android:textColor="@color/color_tab_selector"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_bold"
            app:textAllCaps="true" />
    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grayBg" />
</LinearLayout>
