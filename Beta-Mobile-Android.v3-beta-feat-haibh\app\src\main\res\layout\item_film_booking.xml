<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="horizontal">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/morning"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/margin_small"
        android:layout_marginTop="@dimen/margin_small"
        android:text="@string/morning"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/oswald_regular"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <vn.zenity.betacineplex.helper.view.BetaHorizScrollview
        android:id="@+id/morningScroll"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/morning"
        app:layout_constraintTop_toTopOf="@+id/morning"
        android:padding="@dimen/margin_small"
        android:scrollbars="none">

        <LinearLayout
            android:id="@+id/listTimeMorning"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <vn.zenity.betacineplex.helper.view.ItemTimeBooking
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:bookSeat="120 trống"
                app:bookTime="8:45" />

            <vn.zenity.betacineplex.helper.view.ItemTimeBooking
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:bookSeat="120 trống"
                app:bookTime="8:45" />

            <vn.zenity.betacineplex.helper.view.ItemTimeBooking
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:bookSeat="120 trống"
                app:bookTime="8:45" />

            <vn.zenity.betacineplex.helper.view.ItemTimeBooking
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                app:bookSeat="120 trống"
                app:bookTime="8:45" />
        </LinearLayout>
    </vn.zenity.betacineplex.helper.view.BetaHorizScrollview>
</androidx.constraintlayout.widget.ConstraintLayout>