[{"SeatName":"J11","Status":{"Name":"<PERSON>àm đường đi","Value":"2","Class":"seat-for-way"},"SeatType":{"Name":"<PERSON><PERSON><PERSON> đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":100,"SoldStatus":null,"ClassStyle":""},{"SeatName":"J10","Status":{"Name":"Làm đường đi","Value":"2","Class":"seat-for-way"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":101,"SoldStatus":null,"ClassStyle":""},{"SeatName":"J9","Status":{"Name":"<PERSON>à<PERSON> đường đi","Value":"2","Class":"seat-for-way"},"SeatType":{"Name":"<PERSON><PERSON><PERSON> đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":102,"SoldStatus":null,"ClassStyle":""},{"SeatName":"J8","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":103,"SoldStatus":1,"ClassStyle":""},{"SeatName":"J7","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":104,"SoldStatus":1,"ClassStyle":""},{"SeatName":"J6","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":105,"SoldStatus":1,"ClassStyle":""},{"SeatName":"J5","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":106,"SoldStatus":4,"ClassStyle":""},{"SeatName":"J4","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":107,"SoldStatus":4,"ClassStyle":""},{"SeatName":"J3","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":108,"SoldStatus":1,"ClassStyle":""},{"SeatName":"J2","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":109,"SoldStatus":1,"ClassStyle":""},{"SeatName":"J1","Status":{"Name":"Đang sử dụng","Value":"1","Class":"seat-used"},"SeatType":{"Name":"Ghế đôi","Value":"9beee28c-8cae-41d0-bd01-b0b22108432c","Background":"#e40b8b"},"SeatIndex":110,"SoldStatus":1,"ClassStyle":""}]],


! Could not find valid couple pair for J11, looking for J12
I/flutter (29668): ! Couple seat J11 has broken/missing pair - hiding
I/flutter (29668): ! Couple seat J10 has broken/missing pair - hiding
I/flutter (29668): ! Could not find valid couple pair for J9, looking for J10
I/flutter (29668): ! Couple seat J9 has broken/missing pair - hiding
I/flutter (29668): ! Couple seat J8 has broken/missing pair - hiding
I/flutter (29668): 🎭 Pairing: J7 (1) with J8 (1) - NO SYNC
I/flutter (29668): ! Couple seat J6 has broken/missing pair - hiding
I/flutter (29668): 🎭 Pairing: J5 (4) with J6 (1) - NO SYNC
I/flutter (29668): ! Couple seat J4 has broken/missing pair - hiding
I/flutter (29668): 🎭 Pairing: J3 (1) with J4 (4) - NO SYNC
I/flutter (29668): ! Couple seat J2 has broken/missing pair - hiding
I/flutter (29668): 🎭 Pairing: J1 (1) with J2 (1) - NO SYNC
I/flutter (29668): 🎭 _getSeatImage: J7 - SoldStatus=1, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J8 - SoldStatus=1
I/flutter (29668):    └─ Using EMPTY image for J7
I/flutter (29668): 🎭 _getSeatImage: J5 - SoldStatus=4, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J6 - SoldStatus=1
I/flutter (29668):    └─ Using SOLD image for J5 (status=4)
I/flutter (29668): 🎭 _getSeatImage: J3 - SoldStatus=1, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J4 - SoldStatus=4
I/flutter (29668):    └─ Using EMPTY image for J3
I/flutter (29668): 🎭 _getSeatImage: J1 - SoldStatus=1, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J2 - SoldStatus=1
I/flutter (29668):    └─ Using EMPTY image for J1
I/flutter (29668): unhandled element <metadata/>; Picture key: Svg loader
I/flutter (29668): 🎭 _getSeatImage: J7 - SoldStatus=1, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J8 - SoldStatus=1
I/flutter (29668):    └─ Using EMPTY image for J7
I/flutter (29668): 🎭 _getSeatImage: J5 - SoldStatus=4, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J6 - SoldStatus=1
I/flutter (29668):    └─ Using SOLD image for J5 (status=4)
I/flutter (29668): 🎭 _getSeatImage: J3 - SoldStatus=1, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J4 - SoldStatus=4
I/flutter (29668):    └─ Using EMPTY image for J3
I/flutter (29668): 🎭 _getSeatImage: J1 - SoldStatus=1, isSelected=false
I/flutter (29668):    └─ CoupleSeat: J2 - SoldStatus=1
I/flutter (29668):    └─ Using EMPTY image for J1
