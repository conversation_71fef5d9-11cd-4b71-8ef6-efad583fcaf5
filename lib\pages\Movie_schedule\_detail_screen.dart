import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/Movie_schedule/_film_choose_time.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/Movie_schedule/widget/news_and_deals_card.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/pages/youtube_play.dart';
import 'package:flutter_app/utils/src/convert_data.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../models/index.dart';
import '../../utils/index.dart';
import '../promotion/index.dart';

class FilmDetailScreen extends StatefulWidget {
  final FilmModel? film;
  final String? filmId; // Used if film object is not passed directly
  final bool fromBooking;

  const FilmDetailScreen({
    super.key,
    this.film,
    this.filmId,
    this.fromBooking = false,
  });

  @override
  State<FilmDetailScreen> createState() => _FilmDetailScreenState();
}

class _FilmDetailScreenState extends State<FilmDetailScreen> {
  FilmModel? _filmDetail;
  List<PromotionItem> _promotions = [];

  bool _isLoading = true;
  bool isEnglish = false;
  String? _error;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    // _filmDetail = widget.film; // Use passed film initially
    // if (_filmDetail == null && widget.filmId != null) {
    _fetchFilmDetails();
    _loadPromotions();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchFilmDetails() async {
    final preps = await SharedPreferences.getInstance();
    isEnglish = preps.getBool('isEnglish') ?? false;
    setState(() {
      _isLoading = true;
      _error = null;
    });
    try {
      final api = RepositoryProvider.of<Api>(context);
      final response = await api.film.getFilmDetail(widget.filmId ?? widget.film?.FilmId ?? "");
      setState(() {
        print(response?.data);
        _filmDetail = FilmModel.fromJson(response?.data);
        // _filmDetail = fetchedFilmData; // Assign fetched data
        _isLoading = false;
      });
      // --- End Placeholder ---
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Failed to load film details: $e";
      });
    }
  }

  void _shareFilm() {
    // Share film information - MATCH iOS/Android implementation
    final String filmName = _filmDetail?.getName() ?? "";
    final String filmId = _filmDetail?.FilmId ?? "";

    // Create film detail URL like Android: ${SHARE_DOMAIN}/chi-tiet-phim.htm?gf=${filmId}
    final String filmDetailUrl = "${ApiService.baseURLWeb}/chi-tiet-phim.htm?gf=$filmId";
    final String shareText = "$filmName\n$filmDetailUrl";
    Share.share(filmDetailUrl);
  }

  // --- Xử lý delegate từ NewsAndDealsCard - exactly like iOS NewsAndDealsViewDelegate ---
  void _onPromotionSelected(PromotionItem news) {
    // Navigate to news detail exactly like iOS newsView(_:didSelected:)
    Navigator.push(context, MaterialPageRoute(builder: (_) => NewsDetailScreen(item: news)));
    print("✅ Cinema Detail: Navigate to News Detail for: ${news.title}");
  }

  void _onShowAllPromotions() {
    // Navigate to news and deals list exactly like iOS newsViewDidShowAll()
    Navigator.push(
        context,
        MaterialPageRoute(
            builder: (_) => const PromotionPage(
                  isHomePage: false,
                )));
    print("✅ Cinema Detail: Navigate to News and Deals Screen");
  }

  Future<void> _loadPromotions() async {
    try {
      print('🔄 Cinema Detail: Loading promotions...');

      // Step 1: Get promotion category like iOS getNewEvent()
      final api = RepositoryProvider.of<Api>(context);
      final categoryResponse = await api.promotion.getNewEvent();

      if (categoryResponse != null && categoryResponse.data != null) {
        final categoryData = categoryResponse.data['content'];

        if (categoryData != null && categoryData.isNotEmpty) {
          final firstCategory = categoryData.first;
          final categoryId = firstCategory['CategoryId'];

          if (categoryId != null) {
            print('📂 Cinema Detail: Found category ID: $categoryId');

            // Step 2: Get promotions for category like iOS getNewForCategory()
            final promotionsResponse = await api.promotion.getItemsByCategory(
              categoryId: categoryId,
              pageNumber: 1,
              pageSize: 4,
            );

            if (promotionsResponse != null && promotionsResponse.data != null) {
              final promotionsData = promotionsResponse.data['content'];

              if (promotionsData != null) {
                // final promotionsList = promotionsData
                //     .map((json) => PromotionItem.fromJson(json))
                //     .toList();
                final promotionsList = promotionsData
                    .map<PromotionItem>((item) => PromotionItem.fromJson(item as Map<String, dynamic>))
                    .toList();
                setState(() {
                  _promotions = promotionsList;
                });

                print('✅ Cinema Detail: Loaded ${_promotions.length} promotions');
              }
            }
          }
        }
      }
    } catch (e) {
      print('❌ Cinema Detail: Error loading promotions: $e');
      setState(() {
        _promotions = [];
      });
    }
  }

  void _navigateToBookingOrShare() {
    _shareFilm();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: appBar(title: 'Đặt vé'),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : _filmDetail == null
                  ? const Center(child: Text('Không có dữ liệu phim')) // TODO: Localize
                  : CustomScrollView(
                      controller: _scrollController,
                      slivers: [
                        _buildSliverAppBar(),
                        SliverList(
                          delegate: SliverChildListDelegate(
                            [
                              _buildFilmInfoSection(),
                              _buildDescriptionSection(),
                              _buildNewsSection(),
                              const SizedBox(height: 70),
                            ],
                          ),
                        ),
                      ],
                    ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: _isLoading || _filmDetail == null
          ? null
          : Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: SizedBox(
                width: double.infinity,
                height: 56, // giống FAB.extended
                child: InkWell(
                  onTap: _navigateToBookingOrShare,
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.indigoAccent, // Màu bắt đầu
                          Colors.blue,
                          Colors.lightBlueAccent // Màu kết thúc
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.share, color: Colors.white),
                        const SizedBox(width: 8),
                        Text(
                          'Promotion.Share'.tr(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
    );
  }

  Widget _buildSliverAppBar() {
    // Get film details for the app bar
    final String logoUrl = _filmDetail?.MainPosterUrl ?? "";
    final String filmName = _filmDetail?.getName() ?? '';
    final String ageRestrictionText = _filmDetail?.getAgeName() ?? ''; // TODO: Implement

    return SliverAppBar(
      expandedHeight: 370.0,
      pinned: true,
      stretch: true,
      backgroundColor: Colors.white,
      leadingWidth: 40,
      // Giảm khoảng cách mặc định
      leading: IconButton(
        icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Text(
        'FilmBooking.FilmDetail'.tr(),
        style: const TextStyle(color: Colors.white),
      ),
      centerTitle: false,
      // flexibleSpace: FlexibleSpaceBar(
      //   stretchModes: const [StretchMode.zoomBackground],
      //   background: InkWell(
      //     onTap: () {
      //       // Navigate to YouTube player if video URL is available
      //       if (_filmDetail?.TrailerURL != null && _filmDetail!.TrailerURL!.isNotEmpty) {
      //         Uri uri = Uri.parse('${_filmDetail?.TrailerURL}');
      //         final idsUrl = uri.queryParameters['v'] ?? '';
      //         Navigator.push(context, MaterialPageRoute(builder: (context) => YoutubePlay(url: idsUrl)));
      //       }
      //     },
      //     child: Stack(
      //       fit: StackFit.expand,
      //       children: [
      //         // Banner Image with Mask
      //         ClipPath(
      //           clipper: BottomRoundedClipper(),
      //           child: logoUrl.isNotEmpty
      //               ? imageNetwork(
      //                   url: '${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}',
      //                   fit: BoxFit.cover,
      //                   height: 220)
      //               : Container(color: Colors.grey),
      //         ),
      //         // Gradient overlay for text visibility might be needed depending on image
      //         Container(
      //           decoration: const BoxDecoration(
      //             gradient: LinearGradient(
      //               colors: [Colors.black54, Colors.transparent],
      //               end: Alignment.bottomCenter,
      //               begin: Alignment.topCenter,
      //             ),
      //           ),
      //         ),
      //         // Play Button
      //         Center(child: Image.asset('assets/icon/opacity.png')),
      //         // Film Logo positioned at the bottom
      //         // Positioned(
      //         //   bottom: 10,
      //         //   left: 16,
      //         //   child: Card(
      //         //     elevation: 4,
      //         //     shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      //         //     child: ClipRRect(
      //         //       borderRadius: BorderRadius.circular(8),
      //         //       // TODO: Use CachedNetworkImage
      //         //       child: logoUrl.isNotEmpty
      //         //           ? Image.network('${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}',
      //         //               height: 100, width: 70, fit: BoxFit.cover)
      //         //           : Container(height: 100, width: 70, color: Colors.grey[300]),
      //         //     ),
      //         //   ),
      //         // ),
      //       ],
      //     ),
      //   ),
      // ),
      flexibleSpace: FlexibleSpaceBar(
        stretchModes: const [StretchMode.zoomBackground],
        background: InkWell(
          onTap: () {
            // Navigate to YouTube player if video URL is available
            if (_filmDetail?.TrailerURL != null && _filmDetail!.TrailerURL!.isNotEmpty) {
              Uri uri = Uri.parse('${_filmDetail?.TrailerURL}');
              final idsUrl = uri.queryParameters['v'] ?? '';
              Navigator.push(context, MaterialPageRoute(builder: (context) => YoutubePlay(url: idsUrl,title: _filmDetail?.Name )));
            }
          },
          child: Stack(
            fit: StackFit.expand,
            children: [
              // Banner Image
              logoUrl.isNotEmpty
                  ? ClipPath(
                      clipper: CircularBottomClipper(),
                      child: imageNetwork(
                          url: '${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}',
                          fit: BoxFit.cover,
                          height: 200),
                    )
                  : Container(color: Colors.grey),

              // Overlay gradient (optional)
              Container(
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.black54, Colors.white24],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
              ),

              // Play button
              Center(child: Image.asset('assets/icon/opacity.png')),

              // ✅ Poster Positioned dưới cùng của banner
              Positioned(
                top: 240,
                left: 8,
                right: 4,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Poster
                    Card(
                      elevation: 4,
                      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(5),
                        child: logoUrl.isNotEmpty
                            ? Image.network(
                                '${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}',
                                height: 160,
                                width: 100,
                                fit: BoxFit.cover,
                              )
                            : Container(height: 150, width: 70, color: Colors.grey[300]),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Tên phim + độ tuổi
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            filmName,
                            style: Theme.of(context)
                                .textTheme
                                .headlineSmall
                                ?.copyWith(fontWeight: FontWeight.bold, fontSize: 18),
                            maxLines: 2,
                          ),
                          const SizedBox(height: 4),
                          if (ageRestrictionText.isNotEmpty)
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(color: Colors.grey),
                              ),
                              child: Text(
                                ageRestrictionText,
                                style: const TextStyle(color: Colors.grey, fontSize: 14, fontWeight: FontWeight.bold),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),

      // Title area in collapsed AppBar (optional)
      // title: Text(filmName, style: TextStyle(color: Colors.black)), // Appears when collapsed
    );
  }

  Widget _buildFilmInfoSection() {
    final String logoUrl = _filmDetail?.MainPosterUrl ?? "";

    final String filmName = _filmDetail?.getName() ?? '';
    final String ageRestrictionText = _filmDetail?.getAgeName() ?? ''; // TODO: Implement
    // final Color ageRestrictionColor = _filmDetail?.getAgeRestrictionColor() ?? Colors.grey; // TODO: Implement

    // TODO: Get other details: Director, Actor, Type, Duration, Language, DateShow
    final String director = _filmDetail?.Director ?? '';
    final String actors = _filmDetail?.Actors ?? '';
    final String genre = _filmDetail?.getFilmGenre() ?? ''; // TODO: Implement
    final String duration = '${_filmDetail?.Duration ?? 0} phút'; // TODO: Localize 'minute'
    final String language = _filmDetail?.MainLanguage ?? ''; // TODO: Implement
    final String openingDate = Convert.date(_filmDetail?.OpeningDate ?? ""); // TODO: Implement

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Row(
          //   children: [
          //     Card(
          //       elevation: 4,
          //       shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
          //       child: ClipRRect(
          //         borderRadius: BorderRadius.circular(5),
          //         // TODO: Use CachedNetworkImage
          //         child: logoUrl.isNotEmpty
          //             ? Image.network('${ApiService.baseUrlImage}/${Uri.decodeComponent(logoUrl)}',
          //                 height: 150, width: 100, fit: BoxFit.cover)
          //             : Container(height: 150, width: 70, color: Colors.grey[300]),
          //       ),
          //     ),
          //     const HSpacer(CSpace.lg),
          //     Expanded(
          //       child: Column(
          //         crossAxisAlignment: CrossAxisAlignment.start,
          //         children: [
          //           Text(
          //             filmName,
          //             style: Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
          //             maxLines: 2,
          //           ),
          //           if (ageRestrictionText.isNotEmpty)
          //             Container(
          //               padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          //               decoration: BoxDecoration(
          //                   color: Colors.white,
          //                   borderRadius: BorderRadius.circular(12),
          //                   border: Border.all(color: Colors.grey)),
          //               child: Text(
          //                 ageRestrictionText,
          //                 style: const TextStyle(color: Colors.grey, fontSize: 14, fontWeight: FontWeight.bold),
          //               ),
          //             ),
          //         ],
          //       ),
          //     ),
          //   ],
          // ),
          // const SizedBox(height: 16),
          _buildInfoRow('Film.FilmDetail.Director'.tr(), director),
          _buildInfoRow('Film.FilmDetail.Actor'.tr(), actors),
          _buildInfoRow('Film.FilmDetail.Type'.tr(), genre),
          _buildInfoRow('Film.FilmDetail.Duration'.tr(), duration),
          _buildInfoRow('Film.FilmDetail.Language'.tr(), language),
          _buildInfoRow('Film.FilmDetail.DateShow'.tr(), openingDate),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String title, String content) {
    if (content.isEmpty) return const SizedBox.shrink();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 150, // Adjust width as needed
            child: Text(
              title.toUpperCase(),
              style: Theme.of(context)
                  .textTheme
                  .bodySmall
                  ?.copyWith(fontWeight: FontWeight.bold, color: Colors.black, fontSize: CFontSize.base),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(content, style: Theme.of(context).textTheme.bodyMedium?.copyWith(fontSize: CFontSize.base)),
          ),
        ],
      ),
    );
  }

  Widget _buildDescriptionSection() {
    // TODO: Get description from _filmDetail
    final String description = (isEnglish ? _filmDetail?.ShortDescription_F : _filmDetail?.ShortDescription) ?? '';
    if (description.isEmpty) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(height: 1.2, fontSize: CFontSize.base)),
        ],
      ),
    );
  }

  Widget _buildNewsSection() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_promotions.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text("Promotion.Title".tr(),
                      style: const TextStyle(fontWeight: FontWeight.bold, fontSize: CFontSize.xl)),
                  OutlinedButton(
                    style: OutlinedButton.styleFrom(
                        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                        foregroundColor: Colors.indigo),
                    onPressed: _onShowAllPromotions,
                    child: Text("Promotion.ViewAll".tr(),
                        style: const TextStyle(color: Color(0xFF007AFF), fontSize: CFontSize.base)),
                  ),
                ],
              ),
            ),
            // Use NewsAndDealsCard exactly like iOS NewAndDealsView
            NewsAndDealsCard(
              data: _promotions,
              onItemSelected: _onPromotionSelected,
              onShowAll: _onShowAllPromotions,
            ),
          ],
          const SizedBox(height: 10),
        ],
      ),
    );
  }
}

class CircularBottomClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height - 100); // Đi xuống trước khi cong

    // Đường cong tròn lên
    path.quadraticBezierTo(
      size.width / 2,
      size.height - 60,
      size.width,
      size.height - 100,
    );

    path.lineTo(size.width, 0);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}
