<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_margin="@dimen/margin_small"
    app:cardCornerRadius="3dp"
    app:cardElevation="0dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAreaTitle"
            style="@style/TextContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="15dp"
            tools:text="TP.Ha Noi"
            android:textColor="@color/black" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAreaCountCinema"
            style="@style/TextContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="8dp"
            tools:text="10"
            android:textColor="@color/colorPrimaryDark" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivDropdown"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="12dp"
            tools:rotation="90"
            app:srcCompat="@drawable/ic_zipup" />
    </LinearLayout>
</androidx.cardview.widget.CardView>