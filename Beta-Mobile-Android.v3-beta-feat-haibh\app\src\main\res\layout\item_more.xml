<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@drawable/bg_white_shadow_radius_8dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/clickable"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:layout_gravity="center"
        android:paddingTop="24dp"
        android:paddingBottom="24dp"
        android:paddingLeft="24dp"
        android:paddingRight="24dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/viewBg"
            android:layout_width="70dp"
            android:layout_height="70dp"
            app:srcCompat="@drawable/shape_white_radius"
            android:alpha="0.1"
            app:tint="#0093ee"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivIcon"
            android:layout_width="30dp"
            android:layout_height="30dp"
            app:layout_constraintTop_toTopOf="@+id/viewBg"
            app:layout_constraintBottom_toBottomOf="@+id/viewBg"
            app:layout_constraintLeft_toLeftOf="@+id/viewBg"
            app:layout_constraintRight_toRightOf="@+id/viewBg"
            app:srcCompat="@drawable/ic_more_voucher_free"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvMoreName"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:textSize="16sp"
            android:textColor="@color/text223849"
            android:fontFamily="@font/oswald_regular"
            android:layout_marginTop="8dp"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/viewBg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            tools:text="Voucher Miễn Phí"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>