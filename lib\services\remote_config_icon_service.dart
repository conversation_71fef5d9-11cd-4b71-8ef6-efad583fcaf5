import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:changeicon/changeicon.dart';

class RemoteConfigIconService {
  static final RemoteConfigIconService _instance = RemoteConfigIconService._internal();
  factory RemoteConfigIconService() => _instance;
  RemoteConfigIconService._internal();

  static const String _iconNameKey = 'icon_name';

  // Icon mapping
  static const Map<String, String> _iconMapping = {
    '': 'MainActivity',           // Default icon (ic_launcher)
    'Heart': 'MainActivityHeart', // Heart icon (ic_launcher_heart)
    'Mung_2_thang_9': 'MainActivityVn', // Vietnam icon (ic_launcher_vn)
  };

  final _changeiconPlugin = Changeicon();
  FirebaseRemoteConfig? _remoteConfig;

  /// Initialize Remote Config and icon change plugin
  Future<void> initialize() async {
    try {
      // Initialize changeicon plugin
      await Changeicon.initialize(
        classNames: ['MainActivity', 'MainActivityHeart', 'MainActivityVn'],
      );

      // Initialize Firebase Remote Config
      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set config settings
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(microseconds: 500),
        minimumFetchInterval: const Duration(hours: 1),

      ));

      // Set default values
      await _remoteConfig!.setDefaults({
        _iconNameKey: '', // Default to empty string (MainActivity)
      });

      print('✅ RemoteConfigIconService initialized successfully');
    } catch (e) {
      print('❌ Error initializing RemoteConfigIconService: $e');
    }
  }

  /// Fetch remote config and apply icon changes
  Future<void> fetchAndApplyIcon() async {
    if (_remoteConfig == null) {
      print('❌ Remote Config not initialized');
      _remoteConfig = FirebaseRemoteConfig.instance;

      // Set config settings
      await _remoteConfig!.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(microseconds: 500),
        minimumFetchInterval: const Duration(hours: 1),

      ));

      // Set default values
      await _remoteConfig!.setDefaults({
        _iconNameKey: '', // Default to empty string (MainActivity)
      });
      return;
    }

    try {
      // Fetch remote config
      print('🔄 Fetching remote config for icon...');
      await _remoteConfig!.fetchAndActivate();

      // Get icon name from remote config
      final iconName = _remoteConfig!.getString(_iconNameKey);
      print('📱 Remote config icon_name: "$iconName"');

      // Apply icon change
      await _applyIconChange(iconName);

    } catch (e) {
      print('❌ Error fetching remote config: $e');
      // Fallback to default icon on error
      await _applyIconChange('');
    }
  }

  /// Apply icon change based on remote config value
  Future<void> _applyIconChange(String iconName) async {
    try {
      // Get the corresponding class name
      final className = _iconMapping[iconName] ?? 'MainActivity';

      print('🎯 Applying icon change: "$iconName" → $className');

      // Switch to the specified icon
      await _changeiconPlugin.switchIconTo(classNames: [className, '']);

      print('✅ Icon changed successfully to: $className');

    } catch (e) {
      print('❌ Error changing app icon: $e');
    }
  }

  /// Get current icon name from remote config
  String getCurrentIconName() {
    if (_remoteConfig == null) return '';
    return _remoteConfig!.getString(_iconNameKey);
  }

  /// Get display name for icon
  String getIconDisplayName(String iconName) {
    switch (iconName) {
      case 'Heart':
        return 'Heart Icon';
      case 'Mung_2_thang_9':
        return 'Vietnam Icon';
      case '':
      default:
        return 'Default Icon';
    }
  }

  /// Manual icon change (for testing)
  Future<void> changeIconManually(String iconName) async {
    await _applyIconChange(iconName);
  }

  /// Check if icon change is supported
  Future<bool> isIconChangeSupported() async {
    try {
      // You can add platform-specific checks here if needed
      return true;
    } catch (e) {
      return false;
    }
  }
}
