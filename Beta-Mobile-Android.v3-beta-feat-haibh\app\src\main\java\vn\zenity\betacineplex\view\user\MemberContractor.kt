package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.CardClass

/**
 * Created by Zenity.
 */

interface MemberContractor {
    interface View : IBaseView {
        fun updateAvatarSuccess(url: String, fileImage: String)
        fun updateListCard(cards: List<CardClass>)
        fun logoutSuccess()
        fun showUpdateFBPassword()
    }

    interface Presenter : IBasePresenter<View> {
        fun uploadAvatar(fileImage: String, accountId: String)
        fun getUserProfile(accountId: String)
        fun getCardClass()
        fun unregisterFCMToken(token: String)
    }
}
