<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:clickable="true"
        android:focusable="true"
        android:focusableInTouchMode="true">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:clickable="true"
            android:fitsSystemWindows="true"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivVoucher"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                app:layout_constraintDimensionRatio="h, 4:3"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:src="@drawable/test_event" />

            <LinearLayout
                android:id="@+id/action_bar"
                android:layout_width="match_parent"
                android:layout_height="?actionBarSize"
                android:layout_marginTop="@dimen/statusBarHeight"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/btnBack"
                    android:layout_width="?actionBarSize"
                    android:layout_height="?actionBarSize"
                    android:padding="5dp"
                    app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

            </LinearLayout>

            <View
                android:id="@+id/viewTopDetail"
                android:layout_width="0dp"
                android:layout_height="40dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintBottom_toBottomOf="@+id/ivVoucher"/>

            <vn.zenity.betacineplex.helper.view.CurveView
                android:id="@+id/curve"
                android:layout_width="0dp"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                android:layout_height="40dp"
                android:visibility="visible"
                app:cvColor="@color/white"
                app:cvSmooth="40dp"
                app:cvReverse="false"
                app:layout_constraintTop_toTopOf="@+id/viewTopDetail"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:paddingLeft="24dp"
                android:paddingRight="24dp"
                android:paddingTop="1dp"
                android:paddingBottom="24dp"
                android:textColor="@color/text494c62"
                android:textSize="24sp"
                app:fontFamily="@font/sanspro_bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/curve"
                tools:text="Giảm 5K cho mỗi combo" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTitle1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="16dp"
                android:layout_marginRight="16dp"
                android:text="@string/Condition_apply"
                android:textColor="@color/text1e1f28"
                android:textSize="24sp"
                android:visibility="gone"
                app:fontFamily="@font/sanspro_bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

            <vn.zenity.betacineplex.helper.view.TouchyWebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:fontFamily="@font/sanspro_regular"
                android:textSize="@dimen/font_normal"
                android:background="@color/white"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle1"
                android:padding="@dimen/nav_header_vertical_spacing"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    </ScrollView>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnGetVoucher"
        style="@style/ButtonPrimary"
        android:layout_width="match_parent"
        android:layout_height="48dp"
        android:layout_marginLeft="@dimen/activity_vertical_margin"
        android:layout_marginTop="20dp"
        android:layout_marginRight="@dimen/activity_vertical_margin"
        android:layout_marginBottom="20dp"
        android:text="@string/get_voucher_code"
        tools:visibility="gone"
        android:visibility="gone" />
</LinearLayout>
