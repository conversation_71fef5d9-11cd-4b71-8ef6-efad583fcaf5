import 'dart:async';

import 'package:changeicon/changeicon.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/Movie_schedule/_film_choose_time.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/Movie_schedule/model/banner_model.dart';
import 'package:flutter_app/pages/Movie_schedule/model/home_model.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/version_manager.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/utils/route_manager.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';

import '../../constants/index.dart';
import '../../services/remote_config_icon_service.dart';
import '../../widgets/remote_config_icon_manager.dart';
import '../../widgets/update_dialog.dart';
import '../../utils/src/app_icon_manager.dart';
import '_detail_screen.dart';

class MovieSchedule extends StatefulWidget {
  const MovieSchedule({super.key});

  @override
  State<MovieSchedule> createState() => _MovieScheduleState();
}

class _MovieScheduleState extends State<MovieSchedule> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool? isShowing = true;
  bool isLoading = true;
  List<PromoModel> promotions = [];
  HomeModel? homeModel;
  late final sFilm = RepositoryProvider
      .of<Api>(context)
      .film;
  int _currentTabIndex = 1; // Track current tab index for image selection
  bool isEnglish = false;

  // App Icon Test Variables
  // final AppIconManager appcIcon = AppIconManager();

  var _currentIcon;
  bool _isIconSupported = true;
  bool _isIconLoading = false;

  @override
  void initState() {
    super.initState();
    // initIconChange();
    _tabController = TabController(length: 3, vsync: this, initialIndex: 1);
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) return;

      // Determine if we're showing early films
      bool isEarly = _tabController.index == 2;

      setState(() {
        // Tab 0: Coming Soon, Tab 1: Now Showing, Tab 2: Early Showing
        isShowing = _tabController.index == 0 ? false : true;
        _currentTabIndex = _tabController.index; // Update current tab index for image selection
      });

      isLoading = false;
      Future.delayed(const Duration(microseconds: 100)).then((value) {
        context.read<BlocC<FilmModel>>().setPage(
            page: 1,
            api: (filter, page, size, sort) =>
                sFilm.getListFilm(
                  isShowing: isShowing,
                  isEarly: isEarly,
                ),
            format: FilmModel.fromJson);
        isLoading = true;
      });
    });

    loadData();
    checkVersion();
    _initializeIconStatus();

  }

  checkVersion() async {
    try {
      final result = await VersionManager().checkForUpdates(RepositoryProvider.of<Api>(context));

      // Show update dialog if update is available and not dismissed
      if (result.hasUpdate && result.latestVersion != null) {
        final wasDismissed = await VersionManager().wasVersionCheckDismissed(result.latestVersion!);
        if (!wasDismissed && mounted) {
          UpdateDialog.show(
            context,
            result,
            onUpdatePressed: () async {
              await VersionManager().openAppStore();
            },
            onLaterPressed: () async {
              if (result.latestVersion != null) {
                // await VersionManager().dismissVersionCheck(result.latestVersion!);
              }
            },
          );
        }
      }
    } catch (e) {
      print(e);
    }

    // _initializeIconStatus();
  }

  loadData() async {
    final prefs = await SharedPreferences.getInstance();
    isEnglish = prefs.getBool('isEnglish') ?? false;

    sFilm.getInfoHomePage().then((value) {
      if (value != null) {
        final data = MData.fromJson(value.data, HomeModel.fromJson);
        setState(() {
          homeModel = data.data;
        });
      } else {
        throw Exception('Failed to info');
      }
    }).catchError((e) {
      // UDialog().showError(
      //   title: 'Lỗi',
      //   text: 'Failed to load home data: $e',
      // );
    });
    sFilm.getBanner().then((value) {
      if (value != null) {
        final data = MData.fromJson(value.data, PromoModel.fromJson);
        setState(() {
          promotions = data.content.map((e) => e as PromoModel).toList();
        });
      } else {
        throw Exception('Failed to load films');
      }
    }).catchError(
          (e) {
        print(e);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load films: $e')),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context) {
    return PreferredSize(
      preferredSize: Size(MediaQuery
          .of(context)
          .size
          .width, 80),
      child: Container(
        decoration: const BoxDecoration(border: Border(bottom: BorderSide(color: Colors.black12)), color: Colors.white),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                _buildUserProfileButton(context),
                Image.asset(
                  'assets/images/BETA <EMAIL>',
                  height: 42,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMemberInfoItem({
    required String label,
    IconData? icon,
    String? image,
    required VoidCallback onTap,
    Color? iconColor,
  }) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.white.withAlpha(51), // 0.2 * 255 = 51
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null)
              Icon(
                icon,
                color: iconColor ?? Colors.grey,
                size: 12,
              ),
            if (image != null)
              Image.asset(
                'assets/icon/$image',
                width: 16,
                height: 16,
              ),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.black,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserProfileButton(BuildContext context) {
    // Use homeModel as the source of truth (loaded from sFilm.getInfoHomePage())
    final user = homeModel;
    if (user != null) {
      return _buildLoggedInUserProfile(user);
    } else {
      return _buildLoginButton(context);
    }
  }

  Widget _buildLoggedInUserProfile(HomeModel user) {
    return InkWell(
      onTap: () {
        context.pushNamed(CRoute.member);
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.black, width: 1),
                ),
                child: ClipOval(
                  child: user.picture != null && user.picture!.isNotEmpty
                      ? Image.network(
                    '${ApiService.baseUrlImage}${user.picture!}',
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) =>
                        Center(
                          child: Text(
                            user.fullName != null && user.fullName!.isNotEmpty
                                ? user.fullName![0].toUpperCase()
                                : 'U',
                            style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                          ),
                        ),
                  )
                      : Center(
                    child: Text(
                      user.fullName != null && user.fullName!.isNotEmpty ? user.fullName![0].toUpperCase() : 'U',
                      style: const TextStyle(color: Colors.black, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: isEnglish ? 'Hi ' : "Chào ",
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: CFontSize.base,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        TextSpan(
                          text: user.fullName ?? 'User',
                          style: const TextStyle(
                            color: Colors.black,
                            fontSize: CFontSize.base,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildMemberInfoItem(
                        label: user.classCode ?? "",
                        image: '<EMAIL>',
                        onTap: () {
                          context.pushNamed(CRoute.member);
                        },
                      ),
                      const SizedBox(width: 8),
                      _buildMemberInfoItem(
                        label: '${user.availablePoint ?? 0}',
                        image: '<EMAIL>',
                        onTap: () {
                          context.pushNamed(CRoute.member);
                        },
                      ),
                      const SizedBox(width: 8),
                      _buildMemberInfoItem(
                        label: '${user.quantityOfVoucher ?? 0}',
                        image: '<EMAIL>',
                        onTap: () {
                          context.pushNamed(CRoute.member);
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 8),
        ],
      ),
    );
  }

  Widget _buildLoginButton(BuildContext context) {
    return OutlinedButton.icon(
      key: const Key('login_button_homePage'),
      onPressed: () => context.pushNamed(CRoute.login),
      icon: const Icon(Icons.account_circle, color: Colors.blue),
      label: Text(
        'pages.login.login.Log in'.tr(),
        style: const TextStyle(color: Colors.blue),
      ),
      style: OutlinedButton.styleFrom(
        side: const BorderSide(color: Colors.blue),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildAgeIcon(String? age) {
    switch (age) {
      case 'C13':
        return Image.asset('assets/c-13.png', width: 35, height: 35);
      case 'C16':
        return Image.asset('assets/c-16.png', width: 35, height: 35);
      case 'C18':
        return Image.asset('assets/c-18.png', width: 35, height: 35);
      case 'P':
      case 'K':
        return Image.asset('assets/p.png', width: 35, height: 35);
      default:
        return const SizedBox.shrink();
    }
  }

  /// Build tab button - tương tự iOS UIButton approach
  Widget _buildTabButton({
    required String selectedImage,
    required String unselectedImage,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 8),
        child: Image.asset(
          isSelected ? selectedImage : unselectedImage,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to text if image fails to load
            return Container(
              alignment: Alignment.center,
              decoration: BoxDecoration(
                color: isSelected ? Colors.blue.withValues(alpha: 0.1) : Colors.grey.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                isSelected ? 'SELECTED' : 'TAB',
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                  color: isSelected ? Colors.blue : Colors.grey,
                ),
                textAlign: TextAlign.center,
              ),
            );
          },
        ),
      ),
    );
  }

  /// Handle tab tap - tương tự iOS commingTapped, showingTapped, earlyTapped
  void _onTabTapped(int index) {
    if (_currentTabIndex == index) {
      return; // Same as iOS early return if already selected
    }

    setState(() {
      _currentTabIndex = index;
      // Update isShowing based on tab - tương tự iOS stateType logic
      switch (index) {
        case 0: // Coming Soon
          isShowing = false;
          break;
        case 1: // Now Showing
          isShowing = true;
          break;
        case 2: // Early Show
          isShowing = true;
          break;
      }
    });

    // Update TabController to match
    _tabController.animateTo(index);

    // Load data for selected tab - tương tự iOS getData()
    isLoading = false;
    Future.delayed(const Duration(microseconds: 100)).then((value) {
      context.read<BlocC<FilmModel>>().setPage(
          page: 1,
          api: (filter, page, size, sort) =>
              sFilm.getListFilm(
                isShowing: isShowing,
                isEarly: index == 2, // Early show for tab 2
              ),
          format: FilmModel.fromJson);
      isLoading = true;
    });
  }

  /// Initialize icon status for testing
  Future<void> _initializeIconStatus() async {
    try {
      await _iconService.initialize();
      await _iconService.fetchAndApplyIcon();

      print('initializing ss' );
    } catch (e) {
      print('Error initializing icon status: $e');
    }
  }

  // ✅ Use RemoteConfigIconService instead of direct changeicon
  final RemoteConfigIconService _iconService = RemoteConfigIconService();

  void switchAppIcon(String iconName) async {
    try {
      await _iconService.changeIconManually(iconName);
      print('✅ Icon changed to: $iconName');
    } catch (e) {
      print('❌ Error changing icon: $e');
    }
  }

  /// Refresh icon from remote config
  void refreshIconFromRemoteConfig() async {
    try {
      await _iconService.fetchAndApplyIcon();
      final currentIconName = _iconService.getCurrentIconName();
      print('✅ Icon refreshed from remote config: $currentIconName');
    } catch (e) {
      print('❌ Error refreshing icon from remote config: $e');
    }
  }

  Widget _buildIconTestButton() {
    if (!_isIconSupported) {
      return const SizedBox.shrink(); // Hide if not supported
    }

    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Toggle Icon Button
        FloatingActionButton(
          heroTag: "toggle_icon",
          onPressed: _isIconLoading ? null : () => switchAppIcon(''),
          backgroundColor: _currentIcon == AppIcon.hearts
              ? Colors.red
              : Colors.blue,
          child: _isIconLoading
              ? const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              color: Colors.white,
              strokeWidth: 2,
            ),
          )
              : Icon(
            _currentIcon == AppIcon.hearts
                ? Icons.favorite
                : Icons.apps,
            color: Colors.white,
          ),
        ),

        const SizedBox(height: 8),
        // Heart Icon Button
        FloatingActionButton.small(
          heroTag: "heart_icon",
          onPressed: _isIconLoading ? null : () => switchAppIcon('Heart'),
          backgroundColor: Colors.red,
          child: _isIconLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Icon(Icons.favorite, color: Colors.white, size: 20),
        ),

        const SizedBox(height: 8),
        // Vietnam Icon Button
        FloatingActionButton.small(
          heroTag: "vietnam_icon",
          onPressed: _isIconLoading ? null : () => switchAppIcon('Mung_2_thang_9'),
          backgroundColor: Colors.green,
          child: _isIconLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Icon(Icons.flag, color: Colors.white, size: 20),
        ),

        const SizedBox(height: 8),
        // Refresh from Remote Config Button
        FloatingActionButton.small(
          heroTag: "refresh_remote",
          onPressed: _isIconLoading ? null : refreshIconFromRemoteConfig,
          backgroundColor: Colors.orange,
          child: _isIconLoading
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 2,
                  ),
                )
              : const Icon(Icons.refresh, color: Colors.white, size: 20),
        ),

        const SizedBox(height: 4),

      ],
    );
  }

  /// Toggle app icon for testing
  Future<void> _toggleIcon() async {
    setState(() {
      _isIconLoading = true;
    });

    try {
      print('🔄 Toggling app icon...');
      final success = await AppIconManager.toggleIcon();

      if (success) {
        final newIcon = await AppIconManager.getCurrentIcon();
        if (mounted) {
          setState(() {
            _currentIcon = newIcon;
          });

          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('✅ Icon changed to ${AppIconManager.getIconDisplayName(newIcon)}'),
              backgroundColor: Colors.green,
              duration: const Duration(seconds: 2),
            ),
          );
        }
      } else {
        _showIconErrorSnackBar('Failed to toggle icon');
      }
    } catch (e) {
      print('❌ Error toggling icon: $e');
      _showIconErrorSnackBar('Error: $e');
    } finally {
      if (mounted) {
        setState(() {
          _isIconLoading = false;
        });
      }
    }
  }


  /// Show error snackbar for icon operations
  void _showIconErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('❌ $message'),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AuthC, AuthS>(
      listener: (context, authState) {
        // Auto-refresh when user logs in/out
        if (authState.status == AppStatus.success && authState.user != null) {
          print('✅ User logged in, refreshing movie schedule');
          // Refresh homeModel by calling loadData() - this will call sFilm.getInfoHomePage()
          loadData();
        } else if (authState.status == AppStatus.init) {
          print('🔄 User logged out, clearing data');
          setState(() {
            homeModel = null;
          });
        }
      },
      child: Scaffold(
        appBar: _buildAppBar(context),
        floatingActionButton: _buildIconTestButton(),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: CSpace.base),
          child: Column(
            children: [
              // iOS-style button tabs - tương tự NewHomeViewController
              SizedBox(
                height: 40, // Fixed height for tab container
                child: Row(
                  children: [
                    // Coming Soon Button - tương tự commingSoonButton
                    Expanded(
                      child: _buildTabButton(
                        selectedImage: isEnglish
                            ? 'assets/icon/tab/coming soon/<EMAIL>'
                            : 'assets/icon/tab/coming soon/selected_vi@2x .png',
                        unselectedImage: isEnglish
                            ? 'assets/icon/tab/coming soon/<EMAIL>'
                            : 'assets/icon/tab/coming soon/unselect_vi@2x 2.png',
                        isSelected: _currentTabIndex == 0,
                        onTap: () => _onTabTapped(0),
                      ),
                    ),
                    // Now Showing Button - tương tự showingButton
                    Expanded(
                      child: _buildTabButton(
                        selectedImage: isEnglish
                            ? 'assets/icon/tab/now showing/<EMAIL>'
                            : 'assets/icon/tab/now showing/<EMAIL>',
                        unselectedImage: isEnglish
                            ? 'assets/icon/tab/now showing/<EMAIL>'
                            : 'assets/icon/tab/now showing/<EMAIL>',
                        isSelected: _currentTabIndex == 1,
                        onTap: () => _onTabTapped(1),
                      ),
                    ),
                    // Early Show Button - tương tự earlyShowButton
                    Expanded(
                      child: _buildTabButton(
                        selectedImage: isEnglish
                            ? 'assets/icon/tab/sneak/<EMAIL>'
                            : 'assets/icon/tab/sneak/<EMAIL>',
                        unselectedImage: isEnglish
                            ? 'assets/icon/tab/sneak/<EMAIL>'
                            : 'assets/icon/tab/sneak/<EMAIL>',
                        isSelected: _currentTabIndex == 2,
                        onTap: () => _onTabTapped(2),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: CSpace.base),
                        child: PromoCarousel(
                          promos: promotions,
                        ),
                      ),
                      const SizedBox(height: 10),
                      BlocBuilder<BlocC<FilmModel>, BlocS<FilmModel>>(
                        builder: (context, state) =>
                        !isLoading
                            ? const Center(child: CircularProgressIndicator())
                            : WList<FilmModel>(
                          // inputDisplayType: InputDisplayType.inside,
                          onTap: (content) =>
                          content.HasShow == true
                              ? Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => FilmChooseTimeScreen(film: content, fromHome: true)))
                              : Navigator.push(context,
                              MaterialPageRoute(builder: (context) => FilmDetailScreen(film: content))),
                          physics: const NeverScrollableScrollPhysics(),
                          item: (film, index) {
                            return Column(
                              children: [
                                // const SizedBox(height: 10),
                                Stack(
                                  children: [
                                    // Film Poster
                                    imageNetwork(
                                        url:
                                        '${ApiService.baseUrlImage}/${Uri.decodeComponent(film.MainPosterUrl ?? '')}',
                                        borderRadius: BorderRadius.circular(5)),
                                    // Hot Badge
                                    if (film.IsHot)
                                      Positioned(
                                          top: 0,
                                          right: 0,
                                          child:
                                          Image.asset('assets/icon/<EMAIL>', width: 42, height: 42)),
                                    // Age Rating Badge
                                    Positioned(top: 5, left: 5, child: _buildAgeIcon(film.FilmRestrictAgeName)),
                                  ],
                                ),
                                const VSpacer(CSpace.sm),
                                Column(
                                  children: [
                                    if (_currentTabIndex == 0) // Early Show
                                      Text(
                                        Convert.date(film.OpeningDate.toString(), separator: '-'),
                                        style: const TextStyle(
                                            fontFamily: "Oswald",
                                            color: Colors.blue,
                                            fontWeight: FontWeight.w600,
                                            fontSize: CFontSize.sm),
                                      ),
                                    if (_currentTabIndex == 0) const VSpacer(CSpace.sm),
                                    Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 0),
                                      child: Text(
                                        (isEnglish ? film.Name_F : film.Name) ?? '',
                                        overflow: TextOverflow.ellipsis,
                                        style: const TextStyle(
                                            fontFamily: "Oswald",
                                            fontWeight: FontWeight.w600,
                                            fontSize: CFontSize.sm + 1),
                                      ),
                                    ),
                                    if (_currentTabIndex != 0) const VSpacer(CSpace.sm),
                                    if (_currentTabIndex != 0) // Early Show
                                      Text(
                                        '${film.Duration} ${'Home.Minute'.tr()}',
                                        style: const TextStyle(fontSize: CFontSize.sm - 1, color: Colors.black45),
                                      ),
                                  ],
                                ),
                              ],
                            );
                          },
                          displayMode: DisplayMode.grid,
                          format: FilmModel.fromJson,
                          api: (filet, page, size, sort) {
                            // Determine if we're showing early films - tương tự iOS stateType
                            bool isEarly = _currentTabIndex == 2;
                            return sFilm.getListFilm(
                              isShowing: isShowing,
                              isEarly: isEarly,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class PromoCarousel extends StatefulWidget {
  final List<PromoModel> promos;

  const PromoCarousel({super.key, required this.promos});

  @override
  State<PromoCarousel> createState() => _PromoCarouselState();
}

class _PromoCarouselState extends State<PromoCarousel> {
  final PageController _controller = PageController();
  int _currentPage = 0;
  Timer? _autoScrollTimer;

  @override
  void initState() {
    super.initState();

    _autoScrollTimer = Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_controller.hasClients) {
        int nextPage = (_currentPage + 1) % widget.promos.length;
        _controller.animateToPage(
          nextPage,
          duration: const Duration(milliseconds: 2000),
          curve: Curves.easeInOut,
        );
      }
    });

    _controller.addListener(() {
      final page = _controller.page?.round() ?? 0;
      if (page != _currentPage) {
        setState(() => _currentPage = page);
      }
    });
  }

  @override
  void dispose() {
    _autoScrollTimer?.cancel();
    _controller.dispose();
    super.dispose();
  }

  /// Handle banner tap - tương tự iOS routeTapped()
  void _handleBannerTap(PromoModel promo) {
    final routeManager = RouteManager(
      context: context,
      type: RouteType.fromValue(promo.sreenCode),
      params: promo.refId.isNotEmpty ? [promo.refId] : [],
    );

    routeManager.route();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SizedBox(
          height: 150,
          child: PageView.builder(
            controller: _controller,
            itemCount: widget.promos.length,
            itemBuilder: (context, index) {
              final promo = widget.promos[index];
              return Padding(
                padding: const EdgeInsets.symmetric(horizontal: 0.0),
                child: GestureDetector(
                  onTap: () => _handleBannerTap(promo), // ✅ Handle banner tap
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(CSpace.sm),
                    child: imageNetwork(url:
                    "${ApiService.baseUrlImage}/${promo.imageUrl}",
                      fit: BoxFit.fill,
                      width: double.infinity,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
        Positioned(
          bottom: 10,
          left: 10,
          child: // Indicator
          SmoothPageIndicator(
            controller: _controller,
            count: widget.promos.length,
            effect: const ExpandingDotsEffect(
              activeDotColor: Colors.blue,
              dotColor: Colors.grey,
              dotHeight: 8,
              dotWidth: 8,
              spacing: 6,
            ),
          ),
        )
      ],
    );
  }
}
