# Localization Fix Summary

## Vấn đề
Hiện tại trên app đang hiển thị `"%s points will expire on %s"` không thay đổi giá trị tương ứng.

## Nguyên nhân
Flutter's `easy_localization` package sử dụng cú pháp `{}` cho placeholders, không phải `%s` (C-style format).

## Các thay đổi đã thực hiện

### 1. File `assets/translations/vi.json`
```json
// Trước
"AlmostExpired": "%s điểm sẽ hết hạn vào %s",
"Error": "Lỗi: %s",
"donate_voucher_success_alert": "Bạn đã tặng voucher cho tài khoản \n #%s# \n (%s) \n thành công!",
"donate_point_success_alert": "Bạn đã tặng điểm cho tài khoản \n #%s# \n (%s) \n thành công!",

// Sau
"AlmostExpired": "{} điểm sẽ hết hạn vào {}",
"Error": "Lỗi: {}",
"donate_voucher_success_alert": "Bạn đã tặng voucher cho tài khoản \n #{}# \n ({}) \n thành công!",
"donate_point_success_alert": "Bạn đã tặng điểm cho tài khoản \n #{}# \n ({}) \n thành công!",
```

### 2. File `assets/translations/en.json`
```json
// Trước
"AlmostExpired": "%s points will expire on %s",
"Error": "Error: %s",
"donate_voucher_success_alert": "You have donated voucher for account \n #%s# \n (%s) \n success!",
"donate_point_success_alert": "You have donated point for account \n #%s# \n (%s) \n success!",

// Sau
"AlmostExpired": "{} points will expire on {}",
"Error": "Error: {}",
"donate_voucher_success_alert": "You have donated voucher for account \n #{}# \n ({}) \n success!",
"donate_point_success_alert": "You have donated point for account \n #{}# \n ({}) \n success!",
```

## Cách sử dụng trong code

### Đúng (hiện tại trong reward_points_screen.dart)
```dart
Text(
  'Points.AlmostExpired'.tr(args: [almostExpired.toString(), _formatDate(expiredDate)]),
  // ...
)
```

### Kết quả mong đợi
- **Tiếng Việt**: "1000 điểm sẽ hết hạn vào 31/12/2024"
- **Tiếng Anh**: "1000 points will expire on 31/12/2024"

## Cú pháp easy_localization

### 1. Positional placeholders (đã sử dụng)
```dart
// Translation: "{} điểm sẽ hết hạn vào {}"
'Points.AlmostExpired'.tr(args: ['1000', '31/12/2024'])
// Result: "1000 điểm sẽ hết hạn vào 31/12/2024"
```

### 2. Named placeholders (alternative)
```dart
// Translation: "{points} điểm sẽ hết hạn vào {date}"
'Points.AlmostExpired'.tr(namedArgs: {'points': '1000', 'date': '31/12/2024'})
// Result: "1000 điểm sẽ hết hạn vào 31/12/2024"
```

## Test Cases

### Test Case 1: Points.AlmostExpired
- **Input**: `almostExpired = 1500`, `expiredDate = DateTime(2024, 12, 31)`
- **Expected Vietnamese**: "1500 điểm sẽ hết hạn vào 31/12/2024"
- **Expected English**: "1500 points will expire on 31/12/2024"

### Test Case 2: Error message
- **Input**: `'Common.Error'.tr(args: ['Network timeout'])`
- **Expected Vietnamese**: "Lỗi: Network timeout"
- **Expected English**: "Error: Network timeout"

## Debugging

### Kiểm tra localization hoạt động
```dart
// In reward_points_screen.dart
print('Localization key: Points.AlmostExpired');
print('Args: [${almostExpired.toString()}, ${_formatDate(expiredDate)}]');
print('Result: ${'Points.AlmostExpired'.tr(args: [almostExpired.toString(), _formatDate(expiredDate)])}');
```

### Kiểm tra current locale
```dart
print('Current locale: ${context.locale}');
```

## Lưu ý
- Các key `donate_voucher_success_alert` và `donate_point_success_alert` chưa được sử dụng trong Flutter repo, chỉ có trong iOS/Android repo
- Nếu có thêm key nào sử dụng `%s` format, cần sửa tương tự
- Sau khi sửa, cần restart app để reload localization files
