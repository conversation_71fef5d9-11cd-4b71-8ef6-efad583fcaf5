@echo off
set KEYTOOL="C:\Program Files\Java\jdk-17\bin\keytool.exe"

echo 🔍 Complete signature analysis...

rem Check if device connected
echo 📱 Checking device connection...
adb devices

rem Pull installed app
echo 📱 Pulling installed app from device...
adb pull "/data/app/~~h3uGYzN462vQ8hDJNBXqJQ==/com.beta.betacineplex-fuAz2uQd0KKdtYf1U5FrLQ==/base.apk" installed_app.apk

if not exist "installed_app.apk" (
    echo ❌ Failed to pull installed app
    echo 💡 Make sure:
    echo    - Device is connected via USB
    echo    - USB Debugging is enabled
    echo    - App is installed on device
    pause
    exit /b 1
)

echo ✅ Successfully pulled installed app

rem Check file sizes
echo 📊 File information:
dir installed_app.apk
dir build\app\outputs\flutter-apk\app-release.apk 2>nul

rem Build APK if needed
if not exist "build\app\outputs\flutter-apk\app-release.apk" (
    echo 📦 Building new APK...
    flutter build apk --release
)

echo.
echo 🔐 CERTIFICATE COMPARISON:
echo ==========================================

echo.
echo === INSTALLED APP (từ CH Play) ===
%KEYTOOL% -printcert -jarfile installed_app.apk

echo.
echo === NEW APP (Flutter build) ===
%KEYTOOL% -printcert -jarfile build\app\outputs\flutter-apk\app-release.apk

echo.
echo 📊 MD5 FINGERPRINT COMPARISON:
echo ==========================================

rem Extract MD5 fingerprints
for /f "tokens=2" %%a in ('%KEYTOOL% -printcert -jarfile installed_app.apk ^| findstr "MD5:"') do set INSTALLED_MD5=%%a
for /f "tokens=2" %%a in ('%KEYTOOL% -printcert -jarfile build\app\outputs\flutter-apk\app-release.apk ^| findstr "MD5:"') do set NEW_MD5=%%a

echo Installed MD5: %INSTALLED_MD5%
echo New MD5:       %NEW_MD5%

echo.
if "%INSTALLED_MD5%"=="%NEW_MD5%" (
    echo ✅ SIGNATURES MATCH! 
    echo ✅ Safe to update on Google Play
) else (
    echo ❌ SIGNATURES DO NOT MATCH!
    echo ❌ Using different keystores
)

echo.
echo 🗂️ Generated files:
echo - installed_app.apk (from device)
echo - build\app\outputs\flutter-apk\app-release.apk (new build)

pause