<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="20dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVoucherName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        tools:text="<PERSON>á<PERSON> nhận tặng 50 điểm cho tài kho<PERSON>n"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular" />


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVoucherCodeGot"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:layout_marginTop="30dp"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"
        tools:text="(<EMAIL>)" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="30dp"
        android:background="@color/grayLine"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnVoucherOK"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:fontFamily="@font/sanspro_bold"
            android:text="@string/agree"
            android:textColor="@color/colorPrimaryDark" />

    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="5dp" />
</LinearLayout>