<manifest xmlns:android="http://schemas.android.com/apk/res/android">

  <uses-permission android:name="android.permission.INTERNET" /> <!-- C<PERSON><PERSON> quyền call API -->
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" /> <!-- Cấp quyền ví trí hiện tại -->
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="android.permission.ACCESS_LOCATION_EXTRA_COMMANDS" />
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
  <!-- ❌ REMOVED: READ_PRIVILEGED_PHONE_STATE causes crashes on Android 12+ -->
  <!-- This is a system-level permission that regular apps cannot request -->
  <!-- <uses-permission android:name="android.permission.READ_PRIVILEGED_PHONE_STATE" /> -->
  <uses-permission android:name="android.permission.POST_NOTIFICATIONS" /><!-- Cấp quyền thông báo cho android >= 13-->
  <uses-permission android:name="android.permission.READ_CONTACTS" />
  <uses-permission android:name="android.permission.WRITE_CONTACTS" />
  <uses-permission android:name="android.permission.CAMERA" /> <!-- Cấp quyền camera cho quét QR code -->
  <uses-feature android:name="android.hardware.camera" android:required="false" />
  <uses-feature android:name="android.hardware.camera.autofocus" android:required="false" />

  <!-- Facebook permissions - tương tự Android repo -->
  <queries>
    <provider android:authorities="com.facebook.katana.provider.PlatformProvider" /> <!-- allows app to access Facebook app features -->
    <provider android:authorities="com.facebook.orca.provider.PlatformProvider" /> <!-- allows sharing to Messenger app -->
  </queries>

  <application
    android:name="${applicationName}"
    android:icon="@mipmap/ic_launcher"
    android:label="Beta Cinemas"
    android:requestLegacyExternalStorage="true"
    android:usesCleartextTraffic="true">
    <meta-data
      android:name="com.google.android.gms.version"
      android:value="@integer/google_play_services_version" />
    <meta-data
      android:name="com.google.android.geo.API_KEY"
      android:value="AIzaSyAO_SXaEFQ4QHOtFjk_zCQvy-Xa02SegyE" />

    <!-- Default MainActivity -->
    <activity
      android:name=".MainActivity"
      android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
      android:exported="true"
      android:hardwareAccelerated="true"
      android:icon="@mipmap/ic_launcher"
      android:launchMode="singleTop"
      android:theme="@style/LaunchTheme"
      android:windowSoftInputMode="adjustResize">
      <!-- Specifies an Android theme to apply to this Activity as soon as
           the Android process has started. This theme is visible to the user
           while the Flutter UI initializes. After that, this theme continues
           to determine the Window background behind the Flutter UI. -->
      <meta-data
        android:name="io.flutter.embedding.android.SplashScreenDrawable"
        android:resource="@drawable/launch_background" />

      <!-- ✅ Android 12+ splash screen compatibility -->
      <meta-data
        android:name="io.flutter.embedding.android.NormalTheme"
        android:resource="@style/NormalTheme" />
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>

      <!-- Deep link intent filter for MoMo payment return exactly like Android repo -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data
          android:host="momo"
          android:scheme="betacineplexx" />
      </intent-filter>

      <!-- Deep link intent filter for ZaloPay payment return (optional) -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data
          android:host="zalopay"
          android:scheme="betacineplexx" />
      </intent-filter>

      <!-- Deep link intent filter for AirPay payment return (optional) -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data
          android:host="airpay"
          android:scheme="betacineplexx" />
      </intent-filter>

      <!-- Deep link intent filter for ShopeePay payment return (optional) -->
      <intent-filter android:autoVerify="true">
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data
          android:host="shopeepay"
          android:scheme="betacineplexx" />
      </intent-filter>
    </activity>
    <!-- Don't delete the meta-data below.
         This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
    <meta-data
      android:name="flutterEmbedding"
      android:value="2" />

    <!-- Firebase Cloud Messaging Service - tương ứng với iOS notification handling -->
    <service
      android:name="io.flutter.plugins.firebase.messaging.FlutterFirebaseMessagingService"
      android:exported="false">
      <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT" />
      </intent-filter>
    </service>

    <!-- Default notification channel for FCM - matches Android repo -->
    <meta-data
      android:name="com.google.firebase.messaging.default_notification_channel_id"
      android:value="beta_cinemas_notifications" />

    <!-- Default notification icon - matches Android repo -->
    <meta-data
      android:name="com.google.firebase.messaging.default_notification_icon"
      android:resource="@mipmap/ic_launcher" />

    <!-- Default notification color - matches Android repo -->
    <meta-data
      android:name="com.google.firebase.messaging.default_notification_color"
      android:resource="@color/notification_color" />

    <!-- Facebook configuration - tương tự Android repo -->
    <meta-data
      android:name="com.facebook.sdk.ApplicationId"
      android:value="@string/facebook_app_id" />

    <meta-data
      android:name="com.facebook.sdk.ClientToken"
      android:value="@string/facebook_client_token" />

    <!-- Facebook activities - tương tự Android repo -->
    <activity
      android:name="com.facebook.FacebookActivity"
      android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
      android:label="@string/app_name" />

    <activity
      android:name="com.facebook.CustomTabActivity"
      android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.VIEW" />

        <category android:name="android.intent.category.DEFAULT" />
        <category android:name="android.intent.category.BROWSABLE" />

        <data android:scheme="@string/fb_login_protocol_scheme" />
      </intent-filter>
    </activity>
  </application>
  <!-- Required to query activities that can process text, see:
       https://developer.android.com/training/package-visibility?hl=en and
       https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.

       In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin. -->
  <queries>
    <intent>
      <action android:name="android.intent.action.PROCESS_TEXT" />
      <data android:mimeType="text/plain" />
    </intent>
  </queries>
  <!-- Provide required visibility configuration for API level 30 and above -->
  <queries>
    <!-- If your app checks for SMS support -->
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="sms" />
    </intent>
    <!-- If your app checks for call support -->
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="tel" />
    </intent>
    <!--#enddocregion android-queries-->
    <!-- The "https" scheme is only required for integration tests of this package.
         It shouldn't be needed in most actual apps, or show up in the README! -->
    <intent>
      <action android:name="android.intent.action.VIEW" />
      <data android:scheme="https" />
    </intent>
    <!-- If your application checks for inAppBrowserView launch mode support -->
    <intent>
      <action android:name="android.support.customtabs.action.CustomTabsService" />
    </intent>
  </queries>
</manifest>
