package vn.zenity.betacineplex.view.user.voucher

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.PaymentHistory

interface AddVoucherContractor {
    interface View : IBaseView {
        fun showAddVoucherSuccess()
    }

    interface Presenter : IBasePresenter<View> {
        fun registerVoucher(code: String, pin: String)
    }
}