import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';

class SimpleRemoteConfigTest {
  static Future<void> testRemoteConfig() async {
    try {
      debugPrint('🔄 Testing Remote Config...');
      
      // Get Remote Config instance
      final remoteConfig = FirebaseRemoteConfig.instance;
      debugPrint('✅ Got Remote Config instance');
      
      // Set config settings
      await remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: const Duration(minutes: 1),
      ));
      debugPrint('✅ Set config settings');
      
      // Set default values
      await remoteConfig.setDefaults({
        'icon_name': '',
      });
      debugPrint('✅ Set default values');
      
      // Try to fetch
      await remoteConfig.fetchAndActivate();
      debugPrint('✅ Fetched and activated');
      
      // Get value
      final iconName = remoteConfig.getString('icon_name');
      debugPrint('✅ Got icon_name: "$iconName"');
      
    } catch (e, stackTrace) {
      debugPrint('❌ Remote Config test failed: $e');
      debugPrint('❌ Stack trace: $stackTrace');
    }
  }
}
