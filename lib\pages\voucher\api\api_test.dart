import 'dart:convert';
import 'dart:io';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/pages/voucher/model/voucher_model.dart';
import 'package:flutter_app/pages/cinema/model/create_booking_model.dart';
import 'package:flutter_app/service/language_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';

class ApiResponse {
  final bool status;
  final String? message;
  final dynamic data;

  ApiResponse({
    required this.status,
    this.message,
    this.data,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json) {
    return ApiResponse(
      status: json['status'] ?? false,
      message: json['Message'] as String?,
      data: json['ListObject'] ?? json['Data'],
    );
  }
}

class ApiService {
  static const String baseUrl = 'https://api.betacorp.vn';
  static const String baseUrlResource = "https://files.betacorp.vn/files/";
  static const String baseUrlImage = "https://files.betacorp.vn/files/";
  static const String priceURL = "https://www.cinemas.vn/gia-ve/gia-ve-d.htm";
  static const String signalRUrl = "https://www.betacinemas.vn/signalr/hubs";
  static const String baseURLWeb = "https://www.betacinemas.vn";
  static const String mixpanelToken = "f9846dae54c28e43efcc22bee8df21f1";
  static const String googleServiceInfo = "GoogleServiceProd";

  // static const String baseUrl = 'http://dev.api.betacorp.vn';
  // static const String baseUrlResource = "http://dev.files.tms.betacorp.vn/";
  // static const String baseUrlImage = "http://dev.api.betacorp.vn/files/";
  // static const String signalRUrl = "http://dev.betacineplex.vn/signalr/hubs";
  // static const String priceURL = "https://dev.cinemas.betacorp.vn/gia-ve/gia-ve-d.htm";
  // static const String baseURLWeb = "http://dev.betacineplex.vn";
  // static const String googleServiceInfo = "GoogleServiceDev";
  // static const String mixpanelToken = "f9846dae54c28e43efcc22bee8df21f1";


  // Get token from shared preferences
  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(CPref.token);
  }

  // Get headers with authorization
  static Future<Map<String, String>> getHeaders() async {
    final token = await getToken();
    return {
      'Authorization': token != null ? 'Bearer $token' : '',
      "channel": "mobile",
      "Content-Type": "application/json;charset=UTF-8",
      "device-type": Platform.isAndroid ? "android":"ios",
      "language": await LanguageService().isEnglish() ? "en": "vi",
      "sandbox_mode": "0"
    };
  }

  // Check if user is logged in
  static Future<bool> isLoggedIn() async {
    final token = await getToken();
    return token != null && token.isNotEmpty;
  }

  // Get vouchers
  static Future<List<VoucherModel>> getVouchers() async {
    try {
      final headers = await getHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/api/v2/erp/voucher'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);

        if (apiResponse.status && apiResponse.data != null) {
          final List<dynamic> vouchers = apiResponse.data;
          return vouchers.map((voucher) => VoucherModel.fromJson(voucher)).toList();
        } else {
          throw Exception(apiResponse.message ?? 'Failed to load vouchers');
        }
      } else {
        throw Exception('Failed to load vouchers (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to load vouchers: $e');
    }
  }

  // Use voucher
  static Future<bool> useVoucher(String voucherCode) async {
    try {
      final headers = await getHeaders();
      final response = await http.post(
        Uri.parse('$baseUrl/api/v2/erp/voucher/use'),
        headers: headers,
        body: json.encode({'voucherCode': voucherCode}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);
        return apiResponse.status;
      } else {
        throw Exception('Failed to use voucher (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to use voucher: $e');
    }
  }

  // Donate voucher
  static Future<bool> donateVoucher(String voucherCode, String email) async {
    try {
      final headers = await getHeaders();
      final response = await http.post(
        Uri.parse('$baseUrl/api/v2/erp/voucher/donate'),
        headers: headers,
        body: json.encode({
          'voucherId': voucherCode,
          'email': email,
        }),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);
        return apiResponse.status;
      } else {
        throw Exception('Failed to donate voucher (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to donate voucher: $e');
    }
  }

  // Register voucher
  static Future<bool> registerVoucher(String voucherCode) async {
    try {
      final headers = await getHeaders();
      final response = await http.put(
        Uri.parse('$baseUrl/api/v2/erp/voucher/assign'),
        headers: headers,
        body: json.encode({'voucherCode': voucherCode}),
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);
        return apiResponse.status;
      } else {
        throw Exception('Failed to register voucher (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to register voucher: $e');
    }
  }

  // Get voucher history
  static Future<List<VoucherModel>> getVoucherHistory() async {
    try {
      final headers = await getHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/api/v2/erp/voucher/history'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);

        if (apiResponse.status && apiResponse.data != null) {
          final List<dynamic> vouchers = apiResponse.data;
          return vouchers.map((voucher) => VoucherModel.fromJson(voucher)).toList();
        } else {
          throw Exception(apiResponse.message ?? 'Failed to load voucher history');
        }
      } else {
        throw Exception('Failed to load voucher history (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to load voucher history: $e');
    }
  }

  // Get free vouchers
  static Future<List<FreeVoucher>> getFreeVouchers() async {
    try {
      final headers = await getHeaders();
      final response = await http.get(
        Uri.parse('$baseUrl/api/v1/ecm/free-voucher'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);

        if (apiResponse.status && apiResponse.data != null) {
          final List<dynamic> vouchers = apiResponse.data;
          return vouchers.map((voucher) => FreeVoucher.fromJson(voucher)).toList();
        } else {
          throw Exception(apiResponse.message ?? 'Failed to load free vouchers');
        }
      } else {
        throw Exception('Failed to load free vouchers (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to load free vouchers: $e');
    }
  }

  // Get free voucher by storyline ID
  static Future<bool> getFreeVoucher(String storylineId) async {
    try {
      final headers = await getHeaders();
      final response = await http.post(
        Uri.parse('$baseUrl/api/v2/erp/storyline/$storylineId/voucher-code'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final apiResponse = ApiResponse.fromJson(data);
        return apiResponse.status;
      } else {
        throw Exception('Failed to get free voucher (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to get free voucher: $e');
    }
  }

  // Create booking - exactly like iOS/Android booking API
  static Future<String> createBooking(CreateBookingModel bookingModel) async {
    try {
      final headers = await getHeaders();

      // Add Accept header for HTML response exactly like iOS/Android
      final bookingHeaders = {
        ...headers,
        'Accept': 'text/html',
      };

      final response = await http.post(
        Uri.parse('$baseUrl/booking'),
        headers: bookingHeaders,
        body: json.encode(bookingModel.toJson()),
      );

      if (response.statusCode == 200) {
        // Return HTML content directly like iOS/Android
        return response.body;
      } else {
        throw Exception('Failed to create booking (Status code: ${response.statusCode})');
      }
    } catch (e) {
      throw Exception('Failed to create booking: $e');
    }
  }
}
