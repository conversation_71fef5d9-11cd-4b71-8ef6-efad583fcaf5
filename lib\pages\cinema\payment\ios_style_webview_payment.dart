import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_app/pages/cinema/model/create_booking_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_booking_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';

import '../../../cubit/auth.dart';
import '../../../services/deep_link_service.dart';
import '../../Movie_schedule/model/Film_model.dart';
import '../../voucher/api/api_test.dart';
import '../model/cinema_model.dart';
import '../model/ticket_type.dart';

class IOSStyleWebViewPayment extends StatefulWidget {
  final FilmModel? film;
  final String? combo;
  final ListSeatModel? listSeat;
  final String? cinemaId;
  final String? cinemaName;
  final Function(String?) onPaymentSuccess;
  final Function(String?) onPaymentFailed;
  final Function() onPaymentWaiting;
  final Function(String) onPaymentMethodSelected;
  final int? totalPrice;
  final List<SeatModel>? selectedSeats;
  final ShowModel? showTime;
  final String? showId;
  final int? remainingTime;
  final DateTime? timeStartBooking;

  const IOSStyleWebViewPayment({
    super.key,
    this.film,
    this.combo,
    this.listSeat,
    this.cinemaId,
    this.cinemaName,
    required this.onPaymentSuccess,
    required this.onPaymentFailed,
    required this.onPaymentWaiting,
    required this.onPaymentMethodSelected,
    this.totalPrice,
    this.selectedSeats,
    this.showTime,
    this.showId,
    this.remainingTime,
    this.timeStartBooking,
  });

  @override
  State<IOSStyleWebViewPayment> createState() => _IOSStyleWebViewPaymentState();
}

class _IOSStyleWebViewPaymentState extends State<IOSStyleWebViewPayment> {
  InAppWebViewController? webViewController;
  String? _htmlContent;
  bool _isLoading = true;
  String? _currentUrl;
  bool _webViewCreated = false; // Flag to prevent recreation
  Timer? _countdownTimer;
  int _remainingSeconds = 600;
  String? _paymentMethod;
  bool _paymentProcessed = false; // ✅ Flag to prevent duplicate payment processing

  // Payment tracking variables exactly like iOS
  String? _airPayOrderId;

  // For MoMo payment method exactly like iOS
  String? _momoOrderId;
  String? _resultCode;
  String? _requestId;
  String? _transId;
  String? _message;
  String? _responseTime;
  String? _payType;
  String? _extraData;
  String? _partnerCode;

  // For ZaloPay exactly like iOS
  String? _zaloPayAppId;
  String? _zaloPayTransId;
  String? _zaloPayPmcId;
  String? _zaloPayBankCode;
  String? _zaloPayAmount;
  String? _zaloPayDAmount;
  String? _zaloPayStatus;
  String? _zaloPayCheckSum;

  @override
  void initState() {
    super.initState();
    // _remainingSeconds = widget.remainingTime ?? 600; // 10 minutes default

    // ✅ Setup payment callback listener for deeplink returns
    _setupPaymentCallbackListener();

    _loadBookingPayment();
    _startCountdownTimer();
  }

  @override
  void dispose() {
    _stopCountdownTimer();
    // ✅ Remove payment callback listener
    DeepLinkService.instance.setPaymentCallback((data) {});
    webViewController = null; // Clear webview controller to prevent recreation errors
    _webViewCreated = false; // Reset flag
    _paymentProcessed = false; // ✅ Reset payment processing flag
    super.dispose();
  }

  /// Start countdown timer exactly like iOS PaymentViewController
  void _startCountdownTimer() {
    _stopCountdownTimer();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        timer.cancel();
        _handleTimeout();
      }
    });
  }

  void _stopCountdownTimer() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
  }

  void _handleTimeout() {
    // ✅ Prevent duplicate timeout handling
    if (_paymentProcessed) {
      print('⚠️ iOS Style: Payment already processed, ignoring timeout');
      return;
    }
    _paymentProcessed = true;

    if (mounted) {
      widget.onPaymentFailed('Timeout: Session expired');
      Navigator.of(context).pop();
    }
  }

  /// Load booking payment exactly like iOS getPaymentWeb()
  Future<void> _loadBookingPayment() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Create booking model exactly like iOS
      final bookingModel = _createBookingModel();

      // Debug booking model before sending
      print('📤 Booking model JSON: ${bookingModel!.toJson()}');

      // Call API /booking exactly like iOS FilmProvider.rx.request(.booking(bookingModel))
      final response = await RepositoryProvider.of<Api>(context).film.booking(body: bookingModel.toJson());
      final data = response?.data;

      // Debug response data
      print('📥 Booking response data type: ${data.runtimeType}');
      if (data is String) {
        // Look for price information in HTML
        final priceMatches = RegExp(r'(\d{1,3}(?:,\d{3})*)\s*đ').allMatches(data);
        print('💰 Found prices in HTML: ${priceMatches.map((m) => m.group(0)).toList()}');
      }

      setState(() {
        _htmlContent = data;
        _isLoading = false;
      });

      print('✅ iOS Style: Booking HTML loaded successfully');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      print('❌ iOS Style: Failed to load booking payment: $e');
      widget.onPaymentFailed('Failed to load payment: $e');
    }
  }

  /// Create booking model exactly like iOS CreateBookingModel
  CreateBookingModel? _createBookingModel() {
    final ticketTypes = widget.listSeat?.ticketTypes;
    if (widget.showTime?.showId == null || widget.selectedSeats!.isEmpty || ticketTypes == null) {
      return null;
    }

    final normalTicket = ticketTypes.firstWhere((ticket) => ticket.isNormal, orElse: () => TicketType(price: 0));

    final vipTicket = ticketTypes.firstWhere((ticket) => ticket.isVip, orElse: () => TicketType(price: 0));

    final coupleTicket = ticketTypes.firstWhere((ticket) => ticket.isCouple, orElse: () => TicketType(price: 0));

    // Create seats list - use Android style logic (all selected seats)
    final seats = widget.selectedSeats?.map((seat) {
          // Use helper methods like Android style
          final seatType = _getSeatType(seat);
          final ticketTypeId = _getTicketTypeId(seat);
          final price = _getSeatPrice(seat);

          print(
              '🎫 Added seat to booking: ${seat.seatNumber} - Type: $seatType - TicketTypeId: $ticketTypeId - Price: $price');

          return SeatBookingModel(
            seatIndex: seat.seatIndex ?? 0,
            seatName: seat.seatNumber ?? '',
            seatType: seatType,
            ticketTypeId: ticketTypeId,
            price: price,
          );
        }).toList() ??
        [];

    // Calculate total from booking model seats for verification
    final bookingModelTotal = seats.fold<int>(0, (sum, seat) => sum + seat.price);
    print('💰 Booking model total: $bookingModelTotal');
    print('💰 Widget totalPrice: ${widget.totalPrice}');
    print('💰 Selected seats count: ${widget.selectedSeats?.length}');
    print('💰 Booking seats count: ${seats.length}');

    // Calculate countdown exactly like iOS
    final expiredTime =
        widget.timeStartBooking?.add(const Duration(minutes: 10)) ?? DateTime.now().add(const Duration(minutes: 10));
    final countDown = '/Date(${expiredTime.millisecondsSinceEpoch})/';

    return CreateBookingModel(
      showId: widget.showId ?? '',
      seats: seats,
      countDown: countDown,
    );
  }

  /// Update booking info exactly like iOS updateBookingInfo()
  void _updateBookingInfo() {
    if (webViewController == null) return;

    try {
      // Get film info exactly like iOS
      final filmInfo = widget.film?.getFinalOptions() ?? '';
      final dateStr = widget.listSeat?.ngayChieu?.split('T')[0] ?? '';
      final timeStr = widget.listSeat?.gioChieu?.split('T')[1].substring(0, 5) ?? '';

      // Debug price information
      print('💰 DEBUG: widget.totalPrice = ${widget.totalPrice}');
      print('💰 DEBUG: formatted currency = ${_formatCurrency(widget.totalPrice ?? 0)}');

      // Build JavaScript exactly like iOS (method string concatenation style)
      final jsGetBookingInfo = 'var bookingIf = {};' +
          ' bookingIf.FilmName = "${_escapeString(widget.film?.getName() ?? "")}";' +
          ' bookingIf.FilmInfo = "${_escapeString(filmInfo)}";' +
          ' bookingIf.CinemaName = "${_escapeString(widget.listSeat?.tenRap ?? "")}";' +
          ' bookingIf.DateShow = "$dateStr";' +
          ' bookingIf.ShowTime = "$timeStr";' +
          ' bookingIf.Combo = "${_escapeString(widget.combo ?? "")}";' +
          ' bookingIf.TotalMoney = "${_formatCurrency(widget.totalPrice ?? 0)}";' +
          ' bookingIf.Screen = "${_escapeString(widget.listSeat?.phongChieu ?? "")}";' +
          ' bookingIf.FilmPoster = "${ApiService.baseUrlImage}${widget.film?.MainPosterUrl ?? ""}";' +
          ' bookingIf.FilmFormatCode = "${_escapeString(widget.listSeat?.filmFormatCode ?? "")}";' +
          'getBookingInfo(bookingIf);';

      print('💰 DEBUG: JavaScript TotalMoney = "${_formatCurrency(widget.totalPrice ?? 0)}"');

      webViewController!.evaluateJavascript(source: jsGetBookingInfo);
      print('✅ iOS Style: getBookingInfo executed');

      // Set customer info exactly like iOS
      // TODO: Get user info from AuthC
      final user = context.read<AuthC>().state.user;

      // Validate customer data before building JavaScript
      final customerId = user?.accountId ?? "";
      final customerCard = user?.cardNumber ?? "";
      final jsCustomerInfo = 'var cusI = {};' +
          ' cusI.customerId = "$customerId";' + // Get from user context
          ' cusI.customerCard = "$customerCard";' + // Get from user context
          'getCustomerInfo(cusI);';

      webViewController!.evaluateJavascript(source: jsCustomerInfo);
      print('✅ iOS Style: getCustomerInfo $jsCustomerInfo');
    } catch (e) {
      print('❌ iOS Style: Error updating booking info: $e');
    }
  }

  String _escapeString(String input) {
    return input.replaceAll('"', '\\"').replaceAll("'", "\\'").replaceAll('\n', '\\n');
  }

  String _formatCurrency(int amount) {
    // Format currency exactly like iOS toCurrency()
    return amount.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        );
  }

  /// Get seat type exactly like Android style
  String _getSeatType(SeatModel seat) {
    if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
      return 'VIP';
    } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
      return 'DOUBLE';
    } else {
      return 'STARDAR'; // iOS uses "STARDAR" not "STANDARD"
    }
  }

  /// Get ticket type ID exactly like Android style
  String _getTicketTypeId(SeatModel seat) {
    final listSeat = widget.listSeat;
    if (listSeat?.ticketTypes == null) return '';

    if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
      return listSeat!.ticketTypes!
              .firstWhere((t) => t.isVip == true, orElse: () => listSeat.ticketTypes!.first)
              .ticketTypeId ??
          '';
    } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
      return listSeat!.ticketTypes!
              .firstWhere((t) => t.isCouple == true, orElse: () => listSeat.ticketTypes!.first)
              .ticketTypeId ??
          '';
    } else {
      return listSeat!.ticketTypes!
              .firstWhere((t) => t.isNormal == true, orElse: () => listSeat.ticketTypes!.first)
              .ticketTypeId ??
          '';
    }
  }

  /// Get seat price exactly like Android style
  int _getSeatPrice(SeatModel seat) {
    final listSeat = widget.listSeat;
    if (listSeat?.ticketTypes == null) return 0;

    if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
      return listSeat!.ticketTypes!
              .firstWhere((t) => t.isVip == true, orElse: () => listSeat.ticketTypes!.first)
              .price ??
          0;
    } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
      return listSeat!.ticketTypes!
              .firstWhere((t) => t.isCouple == true, orElse: () => listSeat.ticketTypes!.first)
              .price ??
          0;
    } else {
      return listSeat!.ticketTypes!
              .firstWhere((t) => t.isNormal == true, orElse: () => listSeat.ticketTypes!.first)
              .price ??
          0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, result) async {
          if (!didPop) {
            final shouldPop = await _handleBackPress();
            if (shouldPop && context.mounted) {
              Navigator.of(context).pop();
            }
          }
        },
        child: Scaffold(
          backgroundColor: Colors.white,
          appBar: appBar(
            title: 'Booking.Payment'.tr(),
            titleColor: Colors.white,
            leading: IconButton(
              icon: const Icon(Icons.close, color: Colors.white),
              onPressed: _handleBackButton,
            ),
          ),
          body: _isLoading
              ? const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(height: 16),
                      Text('Đang tải trang thanh toán...'),
                    ],
                  ),
                )
              : _buildWebView(),
        ));
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildWebView() {
    if (_htmlContent == null) {
      return const Center(child: Text('No content to display'));
    }

    return InAppWebView(
      key: const ValueKey('ios_style_webview_payment'),
      // Add unique key to prevent recreation
      // Load HTML content with base URL exactly like iOS
      initialData: InAppWebViewInitialData(
        data: _htmlContent!,
        baseUrl: WebUri('${ApiService.baseUrl}/Booking'),
      ),
      initialSettings: InAppWebViewSettings(
        // iOS-like WebView settings
        javaScriptEnabled: true,
        domStorageEnabled: true,
        useWideViewPort: true,
        loadWithOverviewMode: true,
        supportZoom: false,
        builtInZoomControls: false,
        displayZoomControls: false,

        // Security and compatibility
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,

        // Performance
        cacheEnabled: true,
        clearCache: false,
      ),
      onWebViewCreated: (controller) async {
        print('🔗 iOS Style WebView created');
        if (_webViewCreated) {
          print('⚠️ iOS Style WebView already created, skipping setup');
          return;
        }
        _webViewCreated = true;
        webViewController = controller;

        // Add JavaScript interface "scriptHandler" exactly like iOS WKScriptMessageHandler
        controller.addJavaScriptHandler(
            handlerName: "scriptHandler",
            callback: (args) {
              if (!mounted) return;
              final message = args.isNotEmpty ? args[0].toString() : '';
              print('📱 iOS Style WebView received message: $message');
              _handleJavaScriptMessage(message);
            });

        // ✅ Inject JavaScript to track current screen and improve back navigation
        controller.evaluateJavascript(source: '''
          // Track current screen for better back navigation
          window.currentScreen = 'payment';

          // Override ChoosedDiscount to track screen changes
          if (typeof ChoosedDiscount === 'function') {
            const originalChoosedDiscount = ChoosedDiscount;
            ChoosedDiscount = function(type) {
              console.log('📱 iOS Style: Navigating to discount screen: ' + type);
              window.currentScreen = type;
              return originalChoosedDiscount.apply(this, arguments);
            };
          }

          // Add back navigation function for voucher/beta-point screens
          window.goBackToPayment = function() {
            console.log('📱 iOS Style: Going back to payment screen');
            window.currentScreen = 'payment';

            // Hide voucher and beta-point sections
            const voucherSection = document.getElementById('voucher');
            const betaPointSection = document.getElementById('beta-point');
            const couponSection = document.getElementById('coupon');
            const paymentSection = document.getElementById('payment');

            if (voucherSection) voucherSection.style.display = 'none';
            if (betaPointSection) betaPointSection.style.display = 'none';
            if (couponSection) couponSection.style.display = 'none';
            if (paymentSection) paymentSection.style.display = 'block';

            return true;
          };

          console.log('📱 iOS Style: JavaScript navigation helpers injected');
        ''');

        // Add JavaScript interface "logger" for console logging
        controller.addJavaScriptHandler(
            handlerName: "logger",
            callback: (args) {
              if (!mounted) return;
              final logMessage = args.isNotEmpty ? args[0].toString() : '';
              print('📝 iOS Style WebView Logger: $logMessage');
            });
      },
      onConsoleMessage: (controller, consoleMessage) {
        // Capture all console.log messages from JavaScript
        print('🖥️ iOS Style WebView Console [${consoleMessage.messageLevel}]: ${consoleMessage.message}');
      },
      onLoadStart: (controller, url) {
        print('🔄 iOS Style WebView started loading: $url');
        _updateCurrentUrl(url.toString());
      },
      onLoadStop: (controller, url) async {
        print('✅ iOS Style WebView finished loading: $url');

        // Check screen type and update booking info exactly like iOS didFinish
        try {
          final result = await controller.evaluateJavascript(source: "screenType;");
          final screenType = result?.toString().replaceAll('"', '');

          if (screenType == "payment") {
            print('📄 iOS Style: Payment screen detected, updating booking info');
            _updateBookingInfo();

            // Debug: Call getTicketTypeInBooking() and print results
            // _debugGetTicketTypeInBooking();
            //
            // // Debug: Print parsed JSON values from HTML script
            // _debugParsedJsonValues();
          }
        } catch (e) {
          print('❌ iOS Style: Error checking screen type: $e');
        }
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final url = navigationAction.request.url.toString();
        print('🔗 iOS Style: Navigation to: $url');

        // Handle external payment apps exactly like iOS decidePolicyFor
        return await _handleUrlNavigation(url);
      },
    );
  }

  /// Handle back button press - check if WebView can go back first
  void _handleBackButton() async {
    final shouldPop = await _handleBackPress();
    if (shouldPop && mounted) {
      _stopCountdownTimer();
      Navigator.pop(context);
    }
  }

  /// Handle back press - check WebView navigation first
  Future<bool> _handleBackPress() async {
    try {
      if (webViewController != null) {
        // ✅ Check current screen type first.

        final currentScreen = await webViewController!
            .evaluateJavascript(source: "typeof screenType !== 'undefined' ? screenType : 'unknown'");

        final screenType = currentScreen?.toString().replaceAll('"', '') ?? 'payment';

        print('🔙 iOS Style: Current screen type: $screenType');

        if (screenType == 'voucher' || screenType == 'beta-point' || screenType == 'coupon') {
          // User is in voucher/beta-point screen, go back to payment screen
          print('🔙 iOS Style: In discount screen, going back to payment');

          final backResult = await webViewController!.evaluateJavascript(source: "backToMain()");
          print('🔙 iOS Style: Back to payment result: $backResult');

          return false; // Don't pop the screen, just went back to payment
        } else {
          // User is in main payment screen, check if WebView can go back
          final canGoBack = await webViewController!.canGoBack();

          if (canGoBack) {
            print('🔙 iOS Style: WebView can go back, navigating back in WebView');
            await webViewController!.goBack();
            return false; // Don't pop the screen, just go back in WebView
          } else {
            print('🔙 iOS Style: WebView cannot go back, showing cancel confirmation');
            return await _showCancelConfirmation();
          }
        }
      } else {
        print('🔙 iOS Style: WebView controller not available, showing cancel confirmation');
        return await _showCancelConfirmation();
      }
    } catch (e) {
      print('❌ iOS Style: Error in _handleBackPress: $e');
      return await _showCancelConfirmation();
    }
  }

  /// Show cancel confirmation dialog
  Future<bool> _showCancelConfirmation() async {
    return await showDialog<bool>(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return AlertDialog(
              title: const Text('Xác nhận'),
              content: const Text('Bạn có chắc chắn muốn hủy thanh toán?'),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(false),
                  child: const Text('Không'),
                ),
                TextButton(
                  onPressed: () => Navigator.of(context).pop(true),
                  child: const Text('Có'),
                ),
              ],
            );
          },
        ) ??
        false;
  }

  void _updateCurrentUrl(String url) {
    setState(() {
      _currentUrl = url;
    });
  }

  /// Handle URL navigation exactly like iOS decidePolicyFor navigationAction
  Future<NavigationActionPolicy> _handleUrlNavigation(String url) async {
    print('🔗 iOS Style: Processing URL navigation: $url');

    // Handle payment result URLs - CRITICAL for OnePay callback
    if (url.contains('ketquathanhtoan')) {
      print('📄 iOS Style: Payment result URL detected - processing payment result');
      _handlePaymentResult(url);
      return NavigationActionPolicy.ALLOW;
    }

    // Handle AirPay exactly like iOS
    if (url.contains('airpay.vn') && await canLaunchUrl(Uri.parse(url))) {
      _paymentMethod = 'airpay';
      _trackPayment('confirm');
      _launchExternalUrl(url);
      // Extract orderId exactly like iOS
      final uri = Uri.parse(url);
      _airPayOrderId = uri.queryParameters['order_id'];
      return NavigationActionPolicy.CANCEL;
    }

    // Handle Momo exactly like iOS - FIXED URL pattern
    if (url.contains('payment.momo') && await canLaunchUrl(Uri.parse(url))) {
      _paymentMethod = 'momo';
      _trackPayment('confirm');
      _launchExternalUrl(url);
      return NavigationActionPolicy.CANCEL;
    }

    // Handle ZaloPay exactly like iOS
    if (url.contains('gateway.zalopay.vn') && await canLaunchUrl(Uri.parse(url))) {
      _paymentMethod = 'zalopay';
      _trackPayment('confirm');
      _launchExternalUrl(url);
      return NavigationActionPolicy.CANCEL;
    }

    // Handle OnePay domestic exactly like iOS
    if (url.contains('mtf.onepay.vn/onecomm-pay')) {
      _paymentMethod = 'noidia';
      _trackPayment('confirm');
      print('💳 iOS Style: Navigating to OnePay domestic payment');
      return NavigationActionPolicy.ALLOW;
    }

    // Handle OnePay international exactly like iOS
    if (url.contains('mtf.onepay.vn/promotion/vpcpr.op')) {
      _paymentMethod = 'quocte';
      _trackPayment('confirm');
      print('💳 iOS Style: Navigating to OnePay international payment');
      return NavigationActionPolicy.ALLOW;
    }

    return NavigationActionPolicy.ALLOW;
  }

  void _launchExternalUrl(String url) async {
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      print('❌ iOS Style: Error launching external URL: $e');
    }
  }

  void _trackPayment(String type, [String? errorCode, String? errorMsg]) {
    // TODO: Implement tracking exactly like iOS tracking(type:errorCode:errorMsg:)
    // This should call the same tracking service as iOS with same parameters
    print(
        '📊 iOS Style: Tracking payment - Type: $type, Method: $_paymentMethod, ErrorCode: $errorCode, ErrorMsg: $errorMsg');

    // Track payment events exactly like iOS Tracking.swift
    switch (type) {
      case 'confirm':
        // Track confirm_payment event
        break;
      case 'success':
        // Track pay_success event
        break;
      case 'fail':
        // Track pay_fail event
        break;
    }
  }

  /// Handle payment result URL exactly like webview_payment.dart
  void _handlePaymentResult(String url) {
    print('✅ iOS Style: Processing payment result from URL: $url');

    // Extract payment result from URL parameters
    final uri = Uri.parse(url);
    final params = uri.queryParameters;

    // Check for common payment result parameters
    if (params.containsKey('vpc_TxnResponseCode') ||
        params.containsKey('responseCode') ||
        params.containsKey('resultCode')) {
      print('📊 iOS Style: Payment result parameters found: $params');

      // Process payment result
      _processPaymentResult(params);
    } else {
      print('⚠️ iOS Style: No payment result parameters found in URL');
    }
  }

  /// Process payment result parameters exactly like webview_payment.dart
  void _processPaymentResult(Map<String, String> params) {
    // Common success codes for different payment gateways
    final successCodes = ['0', '00', '000'];

    String? responseCode = params['vpc_TxnResponseCode'] ?? params['responseCode'] ?? params['resultCode'];

    // Extract transaction ID from URL parameters
    String? transactionId = params['vpc_TransactionNo'] ?? params['transactionId'] ?? params['tranId'];

    print('💳 iOS Style: Payment response code: $responseCode');
    print('💳 iOS Style: Transaction ID: $transactionId');

    if (responseCode != null && successCodes.contains(responseCode)) {
      print('✅ iOS Style: Payment successful with code: $responseCode');
      _stopCountdownTimer();
      _trackPayment('success');

      // Show success with transaction ID (no need to call getTransactionId())
      _showPaymentSuccessAlertWithTransactionId(transactionId);
    } else {
      print('❌ iOS Style: Payment failed with code: $responseCode');
      _stopCountdownTimer();
      _trackPayment('fail', responseCode, 'Payment failed');

      // Show failure alert
      _showPaymentFailedAlert();
    }
  }

  /// Handle JavaScript messages exactly like iOS WKScriptMessageHandler
  void _handleJavaScriptMessage(String message) {
    print('📱 iOS Style: Processing message: $message');

    switch (message.replaceAll('"', '')) {
      case 'policy':
        // TODO: Navigate to policy screen
        print('📋 iOS Style: Show policy');
        break;

      case 'payment_success':
        _stopCountdownTimer();
        _trackPayment('success');
        print('✅ iOS Style: Payment success');
        // ✅ Pop to home first, then show success dialog
        _handlePaymentSuccessNavigation('Payment completed successfully');
        break;

      case 'awaiting_payment':
        _stopCountdownTimer();
        print('⏳ iOS Style: Payment awaiting');
        // Check if current screen is payment screen exactly like iOS
        if (mounted) {
          _showPaymentAwaitingAlert();
        }
        break;

      case 'payment_failed':
        _stopCountdownTimer();
        _trackPayment('fail', message, 'Alert.PaymentFailed');
        print('❌ iOS Style: Payment failed');
        _showPaymentFailedAlert();
        break;

      case 'booking_seat_failed':
        _stopCountdownTimer();
        print('❌ iOS Style: Booking seat failed');
        _showBookingSeatFailedAlert();
        break;

      default:
        print('❓ iOS Style: Unknown message: $message');
        break;
    }
  }

  /// Handle payment success navigation - pop to home first
  void _handlePaymentSuccessNavigation(String result) {
    if (!mounted) return;

    print('✅ iOS Style: Handling payment success navigation');

    // ✅ Pop to home screen first
    Navigator.of(context).popUntil((route) => route.isFirst);

    // ✅ Then call success callback to show success dialog on home screen
    widget.onPaymentSuccess(result);
  }

  /// Show payment success alert exactly like iOS showAlert(message: "Alert.PaymentSuccess".localized)
  void _showPaymentSuccessAlert() {
    // ✅ Prevent duplicate dialogs
    if (_paymentProcessed) {
      print('⚠️ iOS Style: Payment already processed, ignoring duplicate success alert');
      return;
    }
    _paymentProcessed = true;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thanh toán thành công'),
          content: const Text('Cảm ơn bạn đã đặt vé. Vé của bạn đã được gửi đến email đăng ký.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                // ✅ Pop to home first, then call success callback
                _handlePaymentSuccessNavigation('Payment completed successfully');
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show payment success alert with transaction ID (for OnePay callback)
  void _showPaymentSuccessAlertWithTransactionId(String? transactionId) {
    // ✅ Prevent duplicate dialogs
    if (_paymentProcessed) {
      print('⚠️ iOS Style: Payment already processed, ignoring duplicate success alert with transaction ID');
      return;
    }
    _paymentProcessed = true;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thanh toán thành công'),
          content: const Text('Cảm ơn bạn đã đặt vé. Vé của bạn đã được gửi đến email đăng ký.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                // ✅ Pop to home first, then call success callback
                _handlePaymentSuccessNavigation(transactionId ?? 'Payment completed successfully');
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show payment failed alert exactly like iOS showAlert(message: "Alert.PaymentFailed".localized)
  void _showPaymentFailedAlert() {
    // ✅ Prevent duplicate dialogs
    if (_paymentProcessed) {
      print('⚠️ iOS Style: Payment already processed, ignoring duplicate failed alert');
      return;
    }
    _paymentProcessed = true;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thanh toán thất bại'),
          content: const Text('Thanh toán không thành công. Vui lòng thử lại.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _gotoHome(); // Exactly like iOS AppDelegate.shared.gotoHome()
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show booking seat failed alert exactly like iOS showAlert(message: "Alert.BookingSeatFailed".localized)
  void _showBookingSeatFailedAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Đặt ghế thất bại'),
          content: const Text('Không thể đặt ghế. Vui lòng thử lại.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _gotoHome(); // Exactly like iOS AppDelegate.shared.gotoHome()
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show payment awaiting alert exactly like iOS showAlert(message: "Alert.BookingWaiting".localized)
  void _showPaymentAwaitingAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Đang chờ thanh toán'),
          content: const Text('Giao dịch của bạn đang được xử lý. Vui lòng kiểm tra lại trong lịch sử giao dịch.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _navigateToTransactionHistory(); // Exactly like iOS navigate to transaction history
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Get transaction detail exactly like iOS getTransactionDetail()
  void _getTransactionDetail() {
    webViewController?.evaluateJavascript(source: "getTransactionId();").then((result) {
      final transactionId = result?.toString().replaceAll('"', '');

      if (transactionId != null && transactionId.isNotEmpty && transactionId != 'null') {
        print('✅ iOS Style: Got transaction ID: $transactionId');
        _navigateToTransactionDetail(transactionId);
      } else {
        print('❌ iOS Style: No transaction ID, showing error alert');
        _showErrorAlert();
      }
    }).catchError((error) {
      print('❌ iOS Style: Error getting transaction ID: $error');
      _showErrorAlert();
    });
  }

  /// Show error alert exactly like iOS showAlert(title: "Alert.Error".localized)
  void _showErrorAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Lỗi'),
          content: const Text('Đã xảy ra lỗi. Vui lòng thử lại.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _gotoHome(); // Navigate to home
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Navigate to transaction detail exactly like iOS TransactionDetailViewController
  void _navigateToTransactionDetail(String transactionId) {
    print('🧾 iOS Style: Navigating to transaction detail: $transactionId');

    // ✅ Pop to home first, then call success callback
    _handlePaymentSuccessNavigation(transactionId);
  }

  /// Navigate to transaction history exactly like iOS TransactionHistory
  void _navigateToTransactionHistory() {
    // TODO: Navigate to transaction history screen
    // For now, go to home
    _gotoHome();
  }

  /// Go to home exactly like iOS AppDelegate.shared.gotoHome()
  void _gotoHome() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  /// Debug function to call getTicketTypeInBooking() and print results
  Future<void> _debugGetTicketTypeInBooking() async {
    try {
      print('🔍 iOS Style: Debugging getTicketTypeInBooking()...');

      // Call getTicketTypeInBooking() function and get its return value directly
      final returnValue = await webViewController!.evaluateJavascript(source: "getTicketTypeInBooking();");
      print('✅ iOS Style: getTicketTypeInBooking() return value = $returnValue');

      print('📋 iOS Style: === Values after getTicketTypeInBooking() ===');

      // Get ticketTypeInBooking array (direct value)
      final ticketTypeResult = await webViewController!.evaluateJavascript(source: "ticketTypeInBooking;");
      print('📊 iOS Style: ticketTypeInBooking = $ticketTypeResult');

      // Get bookingInfor.seats (direct value)
      final seatsResult = await webViewController!.evaluateJavascript(source: "bookingInfor.seats;");
      print('🎫 iOS Style: bookingInfor.seats = $seatsResult');

      // Get bookingInfor money information (direct values)
      final totalMoneyResult = await webViewController!.evaluateJavascript(source: "bookingInfor.TotalMoney;");
      print('💰 iOS Style: bookingInfor.TotalMoney = $totalMoneyResult');

      final comboMoneyResult = await webViewController!.evaluateJavascript(source: "bookingInfor.ComboMoney;");
      print('🎁 iOS Style: bookingInfor.ComboMoney = $comboMoneyResult');

      final totalMoneyWithoutTicketResult =
          await webViewController!.evaluateJavascript(source: "bookingInfor.TotalMoneyWithoutTicket;");
      print('🎟️ iOS Style: bookingInfor.TotalMoneyWithoutTicket = $totalMoneyWithoutTicketResult');

      // Get totalMoneySeat variable (direct value)
      final totalMoneySeatResult = await webViewController!.evaluateJavascript(source: "totalMoneySeat;");
      print('💵 iOS Style: totalMoneySeat = $totalMoneySeatResult');

      // Get paymentInfor updates (direct values)
      final paymentTotalMoneyNeedPay =
          await webViewController!.evaluateJavascript(source: "paymentInfor.TotalMoneyNeedPay;");
      print('💳 iOS Style: paymentInfor.TotalMoneyNeedPay = $paymentTotalMoneyNeedPay');

      final paymentTotalDiscount = await webViewController!.evaluateJavascript(source: "paymentInfor.TotalDiscount;");
      print('🎯 iOS Style: paymentInfor.TotalDiscount = $paymentTotalDiscount');

      // Get UI text updates (direct values)
      final totalMoneyNameText = await webViewController!.evaluateJavascript(source: r'$(".total-money-name").text();');
      print('🏷️ iOS Style: .total-money-name text = $totalMoneyNameText');

      final moneyNeedPayText = await webViewController!.evaluateJavascript(source: r'$(".money-need-pay").text();');
      print('💸 iOS Style: .money-need-pay text = $moneyNeedPayText');

      // Test console.log (much simpler than webkit messageHandlers)
      await webViewController!.evaluateJavascript(source: '''
        console.log("🎯 Test from getTicketTypeInBooking debug");
        console.log("dataBooking.Seats: " + JSON.stringify(bookingInfor.seats));
        console.log("ticketTypeInBooking: " + JSON.stringify(ticketTypeInBooking));
        console.log("bookingInfor.TotalMoney: " + bookingInfor.TotalMoney);
      ''');

      // Print all the values that the function processes/updates (direct values, no JSON.stringify)s
    } catch (e) {
      print('❌ iOS Style: Error debugging getTicketTypeInBooking(): $e');
    }
  }

  /// Debug function to print listCombo after JSON.parse
  Future<void> _debugParsedJsonValues() async {
    try {
      // Get listCombo after JSON.parse (full JSON string to avoid truncation)
      final listComboResult = await webViewController!.evaluateJavascript(source: "JSON.stringify(listCombo)");
      print('📦 iOS Style: listCombo = $listComboResult');
    } catch (e) {
      print('❌ iOS Style: Error debugging listCombo: $e');
    }
  }

  /// ✅ Setup payment callback listener using DeepLinkService
  void _setupPaymentCallbackListener() {
    print('🔧 iOS Style: Setting up payment callback listener via DeepLinkService');

    DeepLinkService.instance.setPaymentCallback((PaymentCallbackData data) async {
      print('💳 iOS Style: Payment callback received via DeepLinkService: ${data.type}');
      print('📊 iOS Style: Payment data: $data');

      try {
        switch (data.type) {
          case PaymentType.momo:
            print('🔄 iOS Style: Processing MoMo callback...');
            await _handleMoMoPaymentCallback(data);
            break;
          case PaymentType.zalopay:
            print('🔄 iOS Style: Processing ZaloPay callback...');
            await _handleZaloPayPaymentCallback(data);
            break;
          case PaymentType.airpay:
            print('🔄 iOS Style: Processing AirPay callback...');
            await _handleAirPayPaymentCallback(data);
            break;
          case PaymentType.shopeepay:
            print('🔄 iOS Style: Processing ShopeePay callback...');
            await _handleShopeePayPaymentCallback(data);
            break;
        }
      } catch (e) {
        print('❌ iOS Style: Error handling payment callback: $e');
      }
    });

    print('✅ iOS Style: Payment callback listener setup completed');

    // ✅ TEST: Simulate payment callback after 10 seconds for debugging
    Future.delayed(const Duration(seconds: 10), () {
      print('🧪 iOS Style: Testing payment callback simulation...');
      _testPaymentCallback();
    });
  }

  /// ✅ TEST: Simulate payment callback for debugging
  void _testPaymentCallback() {
    print('🧪 iOS Style: Simulating MoMo payment callback for testing...');

    final testData = PaymentCallbackData(
      type: PaymentType.momo,
      orderId: 'TEST_ORDER_123',
      resultCode: '0', // Success
      transId: 'TEST_TRANS_456',
      message: 'Test payment successful',
    );

    _handleMoMoPaymentCallback(testData);
  }

  /// ✅ Handle MoMo payment callback exactly like Android style
  Future<void> _handleMoMoPaymentCallback(PaymentCallbackData data) async {
    try {
      print('🔧 iOS Style: Processing MoMo payment callback...');
      print('   - orderId: ${data.orderId}');
      print('   - resultCode: ${data.resultCode}');
      print('   - transId: ${data.transId}');

      if (webViewController == null) {
        print('❌ iOS Style: WebViewController is null, cannot execute JavaScript');
        return;
      }

      // Call checkMomoTransactionStatus exactly like Android repo
      final jsCall = '''
        console.log('🔧 iOS Style: MoMo callback - orderId: ${data.orderId}, resultCode: ${data.resultCode}');

        if (typeof checkMomoTransactionStatus === "function") {
          console.log('✅ iOS Style: checkMomoTransactionStatus function found, calling...');
          try {
            checkMomoTransactionStatus(
              '${data.orderId ?? ''}',
              '${data.resultCode ?? ''}',
              '${data.requestId ?? ''}',
              '${data.transId ?? ''}',
              '${data.message ?? ''}',
              '${data.responseTime ?? ''}',
              '${data.payType ?? ''}',
              '${data.extraData ?? ''}',
              '${data.partnerCode ?? ''}'
            );
            console.log('✅ iOS Style: checkMomoTransactionStatus called successfully');
          } catch (error) {
            console.error('❌ iOS Style: Error calling checkMomoTransactionStatus:', error);
          }
        } else {
          console.error('❌ iOS Style: checkMomoTransactionStatus function not found');
        }
      ''';

      await webViewController!.evaluateJavascript(source: jsCall);
      print('✅ iOS Style: MoMo payment callback processed successfully');

      // Check if payment was successful and navigate to movie schedule
      if (data.resultCode == '0') {
        print('✅ iOS Style: MoMo payment successful, navigating to movie schedule');
        _navigateToMovieSchedule();
      }

    } catch (e) {
      print('❌ iOS Style: Error handling MoMo payment callback: $e');
    }
  }

  /// ✅ Handle ZaloPay payment callback exactly like Android style
  Future<void> _handleZaloPayPaymentCallback(PaymentCallbackData data) async {
    try {
      print('🔧 iOS Style: Processing ZaloPay payment callback...');
      print('   - appTransId: ${data.appTransId}');
      print('   - status: ${data.status}');

      if (webViewController == null) {
        print('❌ iOS Style: WebViewController is null, cannot execute JavaScript');
        return;
      }

      // Call processZaloPayReturn exactly like Android repo
      final jsCall = '''
        console.log('🔧 iOS Style: ZaloPay callback - appTransId: ${data.appTransId}, status: ${data.status}');

        if (typeof processZaloPayReturn === 'function') {
          processZaloPayReturn('${data.appTransId ?? ''}', '${data.status ?? ''}', '${data.amount ?? ''}');
          console.log('✅ iOS Style: processZaloPayReturn called successfully');
        } else {
          console.log('⚠️ iOS Style: processZaloPayReturn function not found');
        }
      ''';

      await webViewController!.evaluateJavascript(source: jsCall);
      print('✅ iOS Style: ZaloPay payment callback processed successfully');

      // Check if payment was successful and navigate to movie schedule
      if (data.status == '1' || data.status == 'success') {
        print('✅ iOS Style: ZaloPay payment successful, navigating to movie schedule');
        _navigateToMovieSchedule();
      }

    } catch (e) {
      print('❌ iOS Style: Error handling ZaloPay payment callback: $e');
    }
  }

  /// ✅ Handle AirPay payment callback
  Future<void> _handleAirPayPaymentCallback(PaymentCallbackData data) async {
    try {
      print('🔧 iOS Style: Processing AirPay payment callback...');
      // AirPay handling logic here if needed
      print('✅ iOS Style: AirPay payment callback processed successfully');
    } catch (e) {
      print('❌ iOS Style: Error handling AirPay payment callback: $e');
    }
  }

  /// ✅ Handle ShopeePay payment callback exactly like Android style
  Future<void> _handleShopeePayPaymentCallback(PaymentCallbackData data) async {
    try {
      print('🔧 iOS Style: Processing ShopeePay payment callback...');
      print('   - orderId: ${data.orderId}');
      print('   - status: ${data.status}');

      if (webViewController == null) {
        print('❌ iOS Style: WebViewController is null, cannot execute JavaScript');
        return;
      }

      // Call processShopeePayReturn exactly like Android repo
      final jsCall = '''
        console.log('🔧 iOS Style: ShopeePay callback - orderId: ${data.orderId}, status: ${data.status}');

        if (typeof processShopeePayReturn === 'function') {
          processShopeePayReturn('${data.orderId ?? ''}', '${data.status ?? ''}', '${data.amount ?? ''}');
          console.log('✅ iOS Style: processShopeePayReturn called successfully');
        } else {
          console.log('⚠️ iOS Style: processShopeePayReturn function not found');
        }
      ''';

      await webViewController!.evaluateJavascript(source: jsCall);
      print('✅ iOS Style: ShopeePay payment callback processed successfully');

      // Check if payment was successful and navigate to movie schedule
      if (data.status == '1' || data.status == 'success') {
        print('✅ iOS Style: ShopeePay payment successful, navigating to movie schedule');
        _navigateToMovieSchedule();
      }

    } catch (e) {
      print('❌ iOS Style: Error handling ShopeePay payment callback: $e');
    }
  }

  /// ✅ Navigate to movie schedule exactly like Android style
  void _navigateToMovieSchedule() {
    try {
      print('🎬 iOS Style: Navigating to movie schedule...');

      if (mounted) {
        // ✅ Prevent duplicate navigation calls
        if (_paymentProcessed) {
          print('⚠️ iOS Style: Payment already processed, ignoring duplicate call');
          return;
        }
        _paymentProcessed = true;

        // ✅ Use new navigation method
        _handlePaymentSuccessNavigation('Payment completed successfully');

        print('✅ iOS Style: Successfully handled payment success navigation');
      } else {
        print('❌ iOS Style: Cannot navigate - widget not mounted');
      }
    } catch (e) {
      print('❌ iOS Style: Error navigating to movie schedule: $e');
    }
  }
}

// ticketTypeInBooking = []
// bookingInfor.seats (with NewPrice) = null
// bookingInfor.TotalMoney = null
// bookingInfor.ComboMoney = null
//  bookingInfor.TotalMoneyWithoutTicket = null
// totalMoneySeat = 0.0
// paymentInfor.TotalMoneyNeedPay = null
// paymentInfor.TotalDiscount = 0.0
//  .total-money-name text = 0đ0đ
// .money-need-pay text = 0đ
