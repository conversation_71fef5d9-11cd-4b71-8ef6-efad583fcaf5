package vn.zenity.betacineplex.view.user

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.appcompat.widget.AppCompatTextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_bookhistory.*
import kotlinx.android.synthetic.main.item_book_history.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.PaymentHistory
import vn.zenity.betacineplex.view.HomeActivity

/**
 * Created by Zenity.
 */

class BookHistoryFragment : BaseFragment(), BookHistoryContractor.View {

    companion object {
        fun getInstance(isShowAirpayAwaiting: Boolean = false): BookHistoryFragment {
            val frag = BookHistoryFragment()
            frag.isShowAirpayAwaiting = isShowAirpayAwaiting
            return frag
        }
    }

    private var isShowAirpayAwaiting = false
    private val presenter = BookHistoryPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_bookhistory
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = Adapter()
        presenter.getPaymentHistory(Global.share().user?.AccountId ?: return)
    }

    override fun showPaymentHistory(historis: List<PaymentHistory>) {
        activity?.runOnUiThread {
            (recyclerView.adapter as? Adapter)?.paymentHistories = historis
            (recyclerView.adapter as? Adapter)?.notifyDataSetChanged()
        }
    }

    override fun back() {
        if (isShowAirpayAwaiting) {
            (activity as? HomeActivity)?.backToHome()
        } else
            super.back()
    }

    override fun onResume() {
        super.onResume()
        presenter.getPaymentHistory(Global.share().user?.AccountId ?: return)
    }

    private inner class Adapter(var paymentHistories: List<PaymentHistory> = listOf()) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return paymentHistories.size + 1
        }

        override fun getItemViewType(position: Int): Int {
            return position
        }

        @SuppressLint("SetTextI18n")
        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (position > 0) {
                holder.itemView.run {
                    val item = paymentHistories[position - 1]
                    tvFilmName.text = item.FilmName
                    tvTime.text = item.ShowTime?.dateConvertFormat(showFormat = Constant.DateFormat.dateTimePaymentHistory, currentFormat = Constant.DateFormat.dateTime)
                    tvCinemaName.text = item.CinemaName
                    tvPrice.text = "${item.TotalPayment.toVNDCurrency()}"
                    tvDiemTichLuy.text = "${item.QuantityPoint}"
                    tvDateExpiredPoint.text = item.DateExpiredPoint?.dateConvertFormat(Constant.DateFormat.defaultFull, Constant.DateFormat.dateSavis)
                    if(item.AirpayLandingUrl.isNullOrEmpty() && item.MomoLandingUrl.isNullOrEmpty() && item.ZaloLandingUrl.isNullOrEmpty()) {
                        textView5.visible()
                        tvDiemTichLuy.visible()
                        textView6.visible()
                        tvDateExpiredPoint.visible()
                        llWaitingProcess.gone()
                    } else {
                        llWaitingProcess.visible()
                        textView5.gone()
                        tvDiemTichLuy.gone()
                        textView6.gone()
                        tvDateExpiredPoint.gone()
                    }
                    setOnClickListener {
                        if(item.AirpayLandingUrl.isNullOrEmpty() && item.MomoLandingUrl.isNullOrEmpty() && item.ZaloLandingUrl.isNullOrEmpty()) {
                            openFragment(BookHistoryDetailFragment.getInstance(item))
                            return@setOnClickListener
                        }
                        item.AirpayLandingUrl?.isNotEmpty()?.let {
                            if (it) {
                                openLink(item.AirpayLandingUrl!!)
                                return@setOnClickListener
                            }
                        }
                        item.MomoLandingUrl?.isNotEmpty()?.let {
                            if (it) {
                                openLink(item.MomoLandingUrl!!)
                                return@setOnClickListener
                            }
                        }
                        item.ZaloLandingUrl?.isNotEmpty()?.let {
                            if (it) {
                                openLink(item.ZaloLandingUrl!!)
                                return@setOnClickListener
                            }
                        }
                    }
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            if(viewType == 0) {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_notice_book_history, parent, false)
                return Holder(item)
            }
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_book_history, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
