# SIGSEGV Crash Fix - CinemaFilmTimeList

## Vấn đề
App Flutter bị crash với lỗi SIGSEGV khi:
- `_checkLoadMoreVisibility` vẫn tiếp tục chạy ngay cả khi user đã chuyển sang màn hình khác (seat.dart)
- Timer không được hủy đúng cách khi widget dispose
- Context được sử dụng sau khi widget đã dispose

## Nguyên nhân
1. **Timer leak**: `_visibilityTimer` không được cancel đúng cách
2. **Context access after dispose**: MediaQuery và setState được gọi sau khi widget dispose
3. **RenderBox access không an toàn**: <PERSON>ó thể gây crash khi widget tree thay đổi
4. **Lifecycle không được quản lý**: Timer vẫn chạy khi app ở background

## Cá<PERSON> thay đổi đã thực hiện

### 1. Thêm WidgetsBindingObserver
```dart
class _CinemaFilmTimeListState extends State<CinemaFilmTimeList> with WidgetsBindingObserver {
  // ...
  
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);  // ✅ Add observer
    checkEnglish();
    _startVisibilityTimer();
  }
  
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // ✅ Pause timer when app goes to background
    if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
      _visibilityTimer?.cancel();
    } else if (state == AppLifecycleState.resumed && !isExpanded && widget.cinemas.length > 5) {
      _startVisibilityTimer();
    }
  }
}
```

### 2. Cải thiện Timer Management
```dart
void _startVisibilityTimer() {
  _visibilityTimer = Timer.periodic(const Duration(milliseconds: 1300), (timer) {
    // ✅ Check mounted first to prevent crashes
    if (!mounted) {
      timer.cancel();
      return;
    }
    
    if (isExpanded || _isLoadingMore || widget.cinemas.length <= 5) {
      return;
    }
    
    _checkLoadMoreVisibility();
  });
}

@override
void dispose() {
  // ✅ Ensure timer is properly cancelled
  _visibilityTimer?.cancel();
  _visibilityTimer = null;
  WidgetsBinding.instance.removeObserver(this);
  super.dispose();
}
```

### 3. Safe Context Access
```dart
void _checkLoadMoreVisibility() {
  // ✅ Double check mounted state before any operations
  if (!mounted) {
    _visibilityTimer?.cancel();
    return;
  }

  final RenderBox? renderBox = _loadMoreKey.currentContext?.findRenderObject() as RenderBox?;
  if (renderBox == null) return;

  try {
    final position = renderBox.localToGlobal(Offset.zero);
    
    // ✅ Check context is still valid before using MediaQuery
    if (!mounted) return;
    
    final screenHeight = MediaQuery.of(context).size.height;
    // ... rest of logic
    
    if (isVisible && mounted) {  // ✅ Double check mounted
      _visibilityTimer?.cancel();
      _loadMoreItems();
    }
  } catch (e) {
    print('❌ Timer visibility check error: $e');
    // ✅ Cancel timer on error to prevent further crashes
    _visibilityTimer?.cancel();
  }
}
```

### 4. Safe setState Operations
```dart
void _loadMoreItems() {
  // ✅ Check mounted state before any operations
  if (!mounted || _isLoadingMore) return;

  setState(() {
    _isLoadingMore = true;
  });

  Future.delayed(const Duration(milliseconds: 500), () {
    // ✅ Double check mounted state before setState
    if (mounted) {
      setState(() {
        isExpanded = true;
        _isLoadingMore = false;
      });
      // ✅ Cancel timer after successful load
      _visibilityTimer?.cancel();
    }
  });
}
```

### 5. Navigation Safety
```dart
void _navigateToSeatSelection(BuildContext context, ShowModel show, ShowCinemaModel cinema) {
  // ✅ Stop timer before navigation to prevent crashes
  _visibilityTimer?.cancel();
  _visibilityTimer = null;

  // ... rest of navigation logic
}
```

## Kết quả mong đợi

### Trước khi fix:
- ❌ SIGSEGV crash khi navigate từ film list sang seat selection
- ❌ Timer vẫn chạy sau khi widget dispose
- ❌ Context được access sau khi dispose
- ❌ Memory leak do timer không được cancel

### Sau khi fix:
- ✅ Không còn SIGSEGV crash
- ✅ Timer được cancel đúng cách khi dispose
- ✅ Context chỉ được access khi widget còn mounted
- ✅ Proper lifecycle management
- ✅ Memory leak được fix

## Test Cases

### Test Case 1: Navigation Safety
1. Mở film list
2. Scroll để trigger timer
3. Tap vào showtime để navigate sang seat selection
4. Verify: Không có crash, timer được cancel

### Test Case 2: App Lifecycle
1. Mở film list với timer running
2. Minimize app (background)
3. Restore app (foreground)
4. Verify: Timer được pause/resume đúng cách

### Test Case 3: Widget Disposal
1. Mở film list
2. Navigate back ngay lập tức
3. Verify: Timer được cancel, không có memory leak

### Test Case 4: Error Handling
1. Trigger visibility check với invalid context
2. Verify: Error được catch, timer được cancel

## Lưu ý
- Tất cả operations giờ đây check `mounted` state trước khi thực hiện
- Timer được cancel ở nhiều điểm để đảm bảo safety
- WidgetsBindingObserver giúp quản lý app lifecycle
- Error handling được cải thiện để prevent crashes
- Memory leaks được fix bằng proper cleanup
