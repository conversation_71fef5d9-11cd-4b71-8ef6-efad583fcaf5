import 'dart:async';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/service/language_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/choose/seat.dart';
import 'package:flutter_app/pages/cinema/widgets/vip_zoom_confirmation_dialog.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../core/index.dart';
import '../../../cubit/index.dart';

class CinemaFilmTimeList extends StatefulWidget {
  final List<ShowCinemaModel> cinemas;
  // final Function(ShowModel, Cinema) onShowSelected;
  final FilmModel film;

  const CinemaFilmTimeList({
    super.key,
    required this.cinemas,
    required this.film,
    // required this.onShowSelected,
  });

  @override
  State<CinemaFilmTimeList> createState() => _CinemaFilmTimeListState();
}

class _CinemaFilmTimeListState extends State<CinemaFilmTimeList> with WidgetsBindingObserver {
  bool isExpanded = false;
  bool isEnglish = false;
  bool _isLoadingMore = false;
  Timer? _visibilityTimer;
  final GlobalKey _loadMoreKey = GlobalKey();

  // ✅ Pagination variables
  int _currentDisplayCount = 5; // Start with 5 items
  static const int _itemsPerPage = 5; // Load 5 more items each time
  bool _hasNavigatedAway = false; // Track if user navigated to another screen

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    checkEnglish();
    _startVisibilityTimer();
  }

  @override
  void dispose() {
    // ✅ Ensure timer is properly cancelled
    _visibilityTimer?.cancel();
    _visibilityTimer = null;
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // ✅ Pause timer when app goes to background to prevent crashes
    if (state == AppLifecycleState.paused || state == AppLifecycleState.detached) {
      _visibilityTimer?.cancel();
    } else if (state == AppLifecycleState.resumed) {
      // ✅ When resuming, check if user navigated away and expand if needed
      if (_hasNavigatedAway) {
        _expandAfterNavigation();
      } else if (_hasMoreItems()) {
        _startVisibilityTimer();
      }
    }
  }

  void _startVisibilityTimer() {
    var startTime = DateTime.now() ;
    _visibilityTimer = Timer.periodic(const Duration(milliseconds: 1300), (timer) {
      // ✅ Check mounted first to prevent crashes
      final elapsed = DateTime.now().difference(startTime);
      if (elapsed.inSeconds >= 7) {
        _loadMoreItems();

        timer.cancel();
        return;
      }
      if (!mounted) {
        timer.cancel();
        return;
      }

      if (_isLoadingMore || !_hasMoreItems()) {
        return;
      }

      _checkLoadMoreVisibility();
    });
  }

  void _checkLoadMoreVisibility() {
    // ✅ Double check mounted state before any operations
    if (!mounted) {
      _visibilityTimer?.cancel();
      return;
    }

    final RenderBox? renderBox = _loadMoreKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox == null) return;

    try {
      final position = renderBox.localToGlobal(Offset.zero);

      // ✅ Check context is still valid before using MediaQuery
      if (!mounted) return;

      final screenHeight = MediaQuery.of(context).size.height;
      final widgetHeight = renderBox.size.height;

      // Check if load more button is visible (at least 50% visible)
      final isVisible = position.dy < screenHeight  &&
                       (position.dy + widgetHeight) > screenHeight * 0.2;

      print('🔍 Timer Check: position.dy=${position.dy.toStringAsFixed(1)}, screenHeight=$screenHeight, isVisible=$isVisible');

      if (isVisible && mounted) {
        print('✅ Timer: Load More button is visible, triggering auto-load');
        _visibilityTimer?.cancel(); // Stop checking
        _loadMoreItems();
      }
    } catch (e) {
      print('❌ Timer visibility check error: $e');
      // ✅ Cancel timer on error to prevent further crashes
      _visibilityTimer?.cancel();
    }
  }

  void _loadMoreItems() {
    // ✅ Check mounted state before any operations
    if (!mounted || _isLoadingMore || !_hasMoreItems()) return;

    print('🔄 CinemaFilmTimeList: Loading more items... Current: $_currentDisplayCount');

    setState(() {
      _isLoadingMore = true;
    });

    // Simulate loading delay for better UX
    Future.delayed(const Duration(milliseconds: 500), () {
      // ✅ Double check mounted state before setState
      if (mounted) {
        setState(() {
          // ✅ Load 5 more items each time
          _currentDisplayCount = (_currentDisplayCount + _itemsPerPage).clamp(0, widget.cinemas.length);
          _isLoadingMore = false;

          // ✅ Check if all items are loaded
          if (_currentDisplayCount >= widget.cinemas.length) {
            isExpanded = true; // Mark as fully expanded
            _visibilityTimer?.cancel(); // Stop timer when all items loaded
          }
        });

        print('✅ CinemaFilmTimeList: Loaded $_itemsPerPage more items. Total showing: $_currentDisplayCount/${widget.cinemas.length}');

        // ✅ Continue timer if there are more items to load
        if (_hasMoreItems() && _visibilityTimer?.isActive != true) {
          _startVisibilityTimer();
        }
      }
    });
  }

  // ✅ Helper method to check if there are more items to load
  bool _hasMoreItems() {
    return _currentDisplayCount < widget.cinemas.length;
  }

  // ✅ Reset pagination to initial state
  void _resetPagination() {
    setState(() {
      _currentDisplayCount = _itemsPerPage;
      isExpanded = false;
      _isLoadingMore = false;
      _hasNavigatedAway = false; // Reset navigation flag
    });
    _visibilityTimer?.cancel();
    if (_hasMoreItems()) {
      _startVisibilityTimer();
    }
  }

  // ✅ Expand list after user returns from navigation
  void _expandAfterNavigation() {
    print('🔄 User returned from navigation, expanding cinema list...');
    setState(() {
      _currentDisplayCount = widget.cinemas.length; // Show all items
      isExpanded = true;
      _isLoadingMore = false;
      _hasNavigatedAway = false; // Reset flag
    });
    _visibilityTimer?.cancel(); // Stop timer since all items are shown
  }
  Future<void> checkEnglish() async {
    final preps = await SharedPreferences.getInstance();
    setState(() {
      isEnglish = preps.getBool('isEnglish') ?? false;
    });
  }
  @override
  Widget build(BuildContext context) {
    // ✅ Use pagination-based display count instead of screen-based calculation
    final displayCinemas = widget.cinemas.take(_currentDisplayCount).toList();

    print('🎬 CinemaFilmTimeList: Displaying ${displayCinemas.length}/${widget.cinemas.length} cinemas');

    return Column(
      children: [
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(), // Keep as non-scrollable since parent handles scroll
            itemCount: displayCinemas.length,
            itemBuilder: (context, index) {
              final cinema = displayCinemas[index];
              return _buildCinemaSection(context, cinema);
            },
          ),
        // Loading indicator when auto-loading more items
        if (_isLoadingMore)
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
                const SizedBox(width: 12),
                Text(tr('FilmBooking.Loading')),
              ],
            ),
          ),

        // ✅ Pagination info
        if (widget.cinemas.length > _itemsPerPage)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Text(
                //   '${tr('FilmBooking.Showing')} ${displayCinemas.length}/${widget.cinemas.length} ${tr('FilmBooking.Cinemas')}',
                //   style: TextStyle(
                //     color: Colors.grey.shade600,
                //     fontSize: 14,
                //   ),
                // ),

                if (isExpanded && widget.cinemas.length > _itemsPerPage) ...[
                  const SizedBox(width: 16),
                  TextButton(
                    onPressed: _resetPagination,
                    child: Text(tr('FilmBooking.ShowLess')),
                  ),
                ],
              ],
            ),
          ),
        // Manual "Load More" button with auto-trigger when visible
        if (_hasMoreItems() && !_isLoadingMore)
          Container(
            key: _loadMoreKey, // Key for visibility detection
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.arrow_drop_down, color: Colors.blue),
                TextButton(
                  onPressed: () {
                    _visibilityTimer?.cancel(); // Stop auto-checking
                    _loadMoreItems(); // ✅ Use pagination load instead of expanding all
                  },
                  child: Text('${tr('FilmBooking.LoadMore')} '),
                ),
              ],
            ),
          ),
      ],
    );
  }

  Widget _buildCinemaSection(BuildContext context, ShowCinemaModel cinema) {
    return GestureDetector(
      onTap: () {
        setState(() {
          cinema.isExpand = !cinema.isExpand;
        });
      },
      child: Card(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
        elevation: 1,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Cinema name and address
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    child: Text(
                   (isEnglish ? cinema.cinemaNameF : cinema.cinemaName )?? 'Unknown Cinema',
                      style: const TextStyle(fontSize: 18),
                    ),
                  ),
                  // Distance (if available)
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (cinema.distance != null && cinema.distance! > 0)
                        Text(
                          '${cinema.distance!.toStringAsFixed(1)} km',
                          style: TextStyle(
                            color: Colors.blue.shade800,
                            fontSize: 16
                          ),
                        ),
                      const SizedBox(width: 8),
                      cinema.isExpand ? CIcon.dropUp : CIcon.dropDown,
                    ],
                  ),
                ],
              ),
              if (cinema.isExpand)
                ...cinema.listFilm?.map((format) => _buildFormatSection(context, format, cinema)) ?? [],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFormatSection(BuildContext context, ListFilmModel format, ShowCinemaModel cinema) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Format name (2D, 3D, etc.)
        const VSpacer(CSpace.base),
        if (format.filmFormatName != null && format.filmFormatName!.isNotEmpty)
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
            (isEnglish ? format.filmFormatNameF?.toUpperCase()  :  format.filmFormatName?.toUpperCase()) ?? '',
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
        // Show times grid
        if ((format.listShow ?? []).isEmpty)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0),
            child: Text(
              'Không có suất chiếu',
              style: TextStyle(
                color: Colors.grey.shade600,
                fontStyle: FontStyle.italic,
              ),
            ),
          )
        else
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              spacing: 8,
              children: (format.listShow ?? []).map((show) {
                final startTime = show.startTime;
                final formatted = startTime != null ? DateFormat('HH:mm').format(startTime) : '--:--';

                // Check if show time is in the past
                final isPast = startTime != null && startTime.isBefore(DateTime.now().toLocal());

                return isPast
                    ? const SizedBox.shrink()
                    : GestureDetector(
                        onTap: () {
                          // Check if showtime requires VIP/Zoom confirmation
                          if (show.screenClass?.toLowerCase() == 'vip') {
                            _showVipZoomConfirmation(context, show, cinema);
                          } else {
                            // Navigate directly to seat selection
                            _checkAgeRestrictionAndProceed(context, show, cinema);
                          }
                        },
                        child: Column(
                          children: [
                            Chip(
                              label: Text(
                                formatted,
                                style: TextStyle(
                                  color: isPast ? Colors.grey : Colors.black,
                                  decoration: isPast ? TextDecoration.lineThrough : null,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 16,
                                ),
                              ),
                              padding: const EdgeInsets.symmetric(horizontal: 8),
                              backgroundColor: isPast ? Colors.grey.shade200 : Colors.grey.shade300,
                            ),
                            Text('${(show.totalSeat ?? 0) - (show.seatSolded ?? 0)} ${'FilmBooking.Empty'.tr()}',style: const TextStyle(fontSize: CFontSize.base),)
                          ],
                        ),
                      );
              }).toList(),
            ),
          ),
        const VSpacer(CSpace.base),
      ],
    );
  }

  /// Show VIP/Zoom confirmation dialog
  Future<void> _showVipZoomConfirmation(BuildContext context, ShowModel show, ShowCinemaModel cinema) async {
    final confirmed = await VipZoomConfirmationDialog.show(
      context: context,
      showTime: show,
      cinemaName: cinema.cinemaName ?? 'Unknown Cinema',
      filmName: widget.film.getName() ?? 'Unknown Film',
    );

    if (confirmed == true) {
      // User confirmed, proceed to seat selection
      _checkAgeRestrictionAndProceed(context, show, cinema);
    }
    // If confirmed is false or null, do nothing (user cancelled)
  }

  /// Navigate to seat selection
  void _navigateToSeatSelection(BuildContext context, ShowModel show, ShowCinemaModel cinema) {
    // ✅ Stop timer before navigation to prevent crashes
    _visibilityTimer?.cancel();
    _visibilityTimer = null;

    final authState = context.read<AuthC>().state;

    if (authState.user == null) {
      _showLoginDialog();
    } else {
      // ✅ Mark that user is navigating away
      _hasNavigatedAway = true;

      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ChooseSeatScreen(
            cinemaId: cinema.cinemaId,
            cinemaName: cinema.cinemaName,
            showTime: show,
            film: widget.film,
          ),
        ),
      ).then((_) {
        // ✅ When user returns from seat selection, expand the list
        if (mounted && _hasNavigatedAway) {
          print('✅ User returned from seat selection, expanding cinema list');
          _expandAfterNavigation();
        }
      });
    }
  }

  void _checkAgeRestrictionAndProceed(BuildContext context,ShowModel show, ShowCinemaModel cinema) {
    // Get restrict age from film - tương tự iOS film age validation
    // final restrictAge = _parseRestrictAge(widget.film.FilmRestrictAgeName ?? widget.film.RestrictAgeString ?? '');
    // if (restrictAge >= 13) {
    //   _showAgeConfirmationDialog(restrictAge, () {
    //     context.pop();
    //     _navigateToSeatSelection(context, show, cinema);
    //   });
    // } else {
      // No age restriction, proceed directly
      _navigateToSeatSelection(context, show, cinema);
    // }
  }
  int _parseRestrictAge(String ageString) {
    if (ageString.isEmpty) return 0;

    // Extract number from strings like "C13", "C16", "C18", "13+", "16+", "18+"
    final regex = RegExp(r'(\d+)');
    final match = regex.firstMatch(ageString);
    if (match != null) {
      return int.tryParse(match.group(1) ?? '0') ?? 0;
    }
    return 0;
  }

  void _showAgeConfirmationDialog(int age, VoidCallback onConfirm) {
    String ageText;
    if (age >= 18) {
      ageText = 'Film.AgeRestrict.C18'.tr();
    } else if (age >= 16) {
      ageText = 'Film.AgeRestrict.C16'.tr();
    } else {
      ageText = 'Film.AgeRestrict.C13'.tr();
    }

    UDialog().showConfirm(
      title: 'Film.AgeRestrict.AgeRestrict'.tr(),
      text: ageText,
      btnOkText: 'Bt.Confirm'.tr(),
      btnCancelText: 'Bt.Cancel'.tr(),
      btnOkOnPress: onConfirm,
    );
  }

  void _showLoginDialog() {
    UDialog().showConfirm(
        title: 'Auth.Login'.tr(),
        text: 'Auth.LoginRequired'.tr(),
        btnOkText: 'Đăng nhập',
        btnOkOnPress: () {
          Navigator.pop(context);
          // ✅ Mark that user is navigating away
          _hasNavigatedAway = true;
          // Navigate to login screen
          context.pushNamed(CRoute.login).then((_) {
            // ✅ When user returns from login, expand the list if they navigated away
            if (mounted && _hasNavigatedAway) {
              print('✅ User returned from login, expanding cinema list');
              _expandAfterNavigation();
            }
          });
        });
  }
}
