<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--Font size-->
    <dimen name="font_very_small">10sp</dimen>
    <dimen name="font_small">12sp</dimen>
    <dimen name="font_normal">16sp</dimen>
    <dimen name="font_medium">18sp</dimen>
    <dimen name="font_large">20sp</dimen>
    <dimen name="font_extra_large">24sp</dimen>
    <dimen name="font_time_call">30sp</dimen>

    <dimen name="padding_small">12dp</dimen>
    <dimen name="padding_normal">16dp</dimen>
    <dimen name="padding_large">20dp</dimen>

    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="nav_header_vertical_spacing">8dp</dimen>
    <dimen name="nav_header_height">176dp</dimen>
    <dimen name="fab_margin">16dp</dimen>
    <dimen name="radius_small">3dp</dimen>
    <dimen name="small_radius">3dp</dimen>
    <dimen name="margin_small">5dp</dimen>
    <dimen name="margin_normal">10dp</dimen>
    <dimen name="margin_large">20dp</dimen>
    <dimen name="statusBarHeight">0dp</dimen>
    <!--<dimen name="statusBarHeight">24dp</dimen>-->
    <dimen name="normal_radius">8dp</dimen>
    <dimen name="width_icon_chair">24dp</dimen>
</resources>