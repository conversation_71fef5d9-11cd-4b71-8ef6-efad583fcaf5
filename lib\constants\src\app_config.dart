/// App Configuration Constants
/// Contains app-specific configuration like store IDs, URLs, etc.
class AppConfig {
  // App Store Configuration
  static const String appStoreId = '1403107666'; // Replace with actual App Store ID
  static const String androidPackageName = 'com.beta.betacineplex'; // From Android manifest

  // Store URLs
  static const String iosAppStoreUrl = 'https://apps.apple.com/app/id$appStoreId';
  static const String androidPlayStoreUrl = 'https://play.google.com/store/apps/details?id=$androidPackageName';

  // Version Check Configuration
  static const Duration versionCheckInterval = Duration(hours: 24); // Check once per day
  static const Duration versionCheckDelay = Duration(seconds: 2); // Delay after app start

  // App Information
  static const String appName = 'Beta Cineplex';
  static const String supportEmail = '<EMAIL>';
  static const String websiteUrl = 'https://betacineplex.vn';

  // Minimum OS Requirements
  static const String minimumIOSVersion = '12.0';
  static const String minimumAndroidVersion = '6.0';
  static const int minimumAndroidSDK = 23;

  // Feature Flags
  static const bool enableVersionCheck = true;
  static const bool enableAutoVersionCheck = true;
  static const bool enableStoreVersionCheck = true;

  // Debug Configuration
  static const bool debugVersionCheck = false; // Set to true for testing
  static const String debugLatestVersion = '2.0.0'; // For testing purposes
}

/// Environment-specific configuration
class EnvironmentConfig {
  static bool get isProduction => const String.fromEnvironment('ENVIRONMENT') == 'production';
  static bool get isDevelopment => const String.fromEnvironment('ENVIRONMENT') == 'development';
  static bool get isStaging => const String.fromEnvironment('ENVIRONMENT') == 'staging';

  // API Endpoints (if different per environment)
  static String get apiBaseUrl {
    if (isProduction) {
      return 'https://api.betacineplex.vn';
    } else if (isStaging) {
      return 'https://staging-api.betacineplex.vn';
    } else {
      return 'https://dev-api.betacineplex.vn';
    }
  }

  // Version check behavior per environment
  static bool get enableVersionCheckInThisEnvironment {
    // Enable version checking in all environments for now
    // In production, you might want to restrict this to specific environments
    return AppConfig.enableVersionCheck;

    // Original logic (commented out for development):
    // return AppConfig.enableVersionCheck && (isProduction || isStaging);
  }
}
