package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.mapCode
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class AccountInformationPresenter : AccountInformationContractor.Presenter {

    private var citis: ArrayList<CityModel>? = null
    private var disposable: Disposable? = null
    private var mapDistrict: HashMap<String, ArrayList<CityModel>> = hashMapOf()

    override fun getCity() {
        if (citis != null) {
            view?.get()?.showListCity(citis!!)
            return
        }
        view?.get()?.showLoading()
        disposable = APIClient.shared.cityAPI.getListCity().applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            citis = it
                            view?.get()?.showListCity(citis!!)
                        }
                    } else {

                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun getDistrict(cityId: String) {
        if (mapDistrict[cityId] != null) {
            view?.get()?.showDistrict(cityId, mapDistrict[cityId]!!)
            return
        }
        view?.get()?.showLoading()
        disposable = APIClient.shared.cityAPI.getDistrictOfCity(cityId).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            mapDistrict[cityId] = it
                            view?.get()?.showDistrict(cityId, it)
                        }
                    } else {

                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun updateProfile(userId: String, profile: RegisterModel) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.updateProfile( userId, profile).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let { user ->
                            view?.get()?.showUpdatedProfile(user)
                        }
                        view?.get()?.updateUserProfileSuccess(it.Message?.mapCode() ?: R.string.update_account_info_success.getString())
                    } else {
                        view?.get()?.showAlert(it.Message?.mapCode() ?: R.string.update_account_info_error.getString())
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.showAlert(it.message?.mapCode() ?: R.string.update_account_info_error.getString())
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<AccountInformationContractor.View?>? = null
    override fun attachView(view: AccountInformationContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}
