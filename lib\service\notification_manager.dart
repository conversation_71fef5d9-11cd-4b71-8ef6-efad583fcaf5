import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/index.dart';
import '../utils/index.dart';
import 'notification_service.dart';

/// Notification Manager - tương ứng với iOS notification management
///
/// Qu<PERSON>n lý:
/// - User login/logout notification registration
/// - App lifecycle notification handling
/// - Notification preferences
/// - Integration với authentication flow
///
/// Tương ứng với iOS App.registerFCMDevice() và notification lifecycle
class NotificationManager {
  static final NotificationManager _instance = NotificationManager._internal();
  factory NotificationManager() => _instance;
  NotificationManager._internal();

  static NotificationManager get instance => _instance;

  final NotificationService _notificationService = NotificationService.instance;
  bool _isUserLoggedIn = false;

  /// Initialize notification manager - g<PERSON>i sau khi app khởi động
  Future<void> initialize() async {
    debugPrint('🔔 Initializing NotificationManager...');

    // Check if user is logged in
    await _checkUserLoginStatus();

    // Register for notifications if user is logged in
    if (_isUserLoggedIn) {
      await registerForNotifications();
    }

    debugPrint('✅ NotificationManager initialized');
  }

  /// Register for notifications when user logs in - tương ứng với iOS registerFCMDevice
  Future<void> registerForNotifications() async {
    debugPrint('🔔 Registering for notifications...');

    try {
      // Request permissions first
      final permissionGranted = await _notificationService.requestPermissions();
      if (!permissionGranted) {
        debugPrint('❌ Notification permissions not granted');
        return;
      }

      // Get FCM token and register with server
      final token = await _notificationService.getFCMToken();
      if (token != null && token.isNotEmpty) {
        await _notificationService.registerFCMToken();
        debugPrint('✅ Successfully registered for notifications');
      } else {
        debugPrint('❌ Failed to get FCM token');
      }
    } catch (e) {
      debugPrint('❌ Error registering for notifications: $e');
    }
  }

  /// Unregister from notifications when user logs out - tương ứng với iOS unregisterFCMToken
  Future<void> unregisterFromNotifications() async {
    debugPrint('🔔 Unregistering from notifications...');

    try {
      await _notificationService.unregisterFCMToken();

      // Clear local notification preferences
      final prefs = await SharedPreferences.getInstance();
      // await prefs.remove(CPref.fcmToken);
      await prefs.remove(CPref.isTokenRegistered);

      _isUserLoggedIn = false;
      debugPrint('✅ Successfully unregistered from notifications');
    } catch (e) {
      debugPrint('❌ Error unregistering from notifications: $e');
    }
  }

  /// Handle user login - register for notifications
  Future<void> onUserLogin(String accountId) async {
    debugPrint('🔔 User logged in, registering for notifications...');

    // Save account ID for notification registration
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(CPref.accountId, accountId);

    _isUserLoggedIn = true;
    await registerForNotifications();
  }

  /// Handle user logout - unregister from notifications
  Future<void> onUserLogout() async {
    debugPrint('🔔 User logged out, unregistering from notifications...');

    await unregisterFromNotifications();

    // Clear user data
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(CPref.accountId);
    await prefs.remove(CPref.user);
  }

  /// Subscribe to notification topics - tương ứng với iOS topic subscription
  Future<void> subscribeToTopics(List<String> topics) async {
    debugPrint('🔔 Subscribing to topics: $topics');

    for (final topic in topics) {
      await _notificationService.subscribeToTopic(topic);
    }
  }

  /// Unsubscribe from notification topics
  Future<void> unsubscribeFromTopics(List<String> topics) async {
    debugPrint('🔔 Unsubscribing from topics: $topics');

    for (final topic in topics) {
      await _notificationService.unsubscribeFromTopic(topic);
    }
  }

  /// Check notification permission status
  Future<bool> checkNotificationPermissions() async {
    return await _notificationService.requestPermissions();
  }

  /// Get current FCM token
  Future<String?> getCurrentFCMToken() async {
    return await _notificationService.getFCMToken();
  }

  /// Check if notifications are enabled
  Future<bool> areNotificationsEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(CPref.notificationEnabled) ?? true;
  }

  /// Enable/disable notifications
  Future<void> setNotificationsEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(CPref.notificationEnabled, enabled);

    if (enabled && _isUserLoggedIn) {
      await registerForNotifications();
    } else if (!enabled) {
      await unregisterFromNotifications();
    }
  }

  /// Handle app foreground - check for pending notifications
  Future<void> onAppForeground() async {
    debugPrint('🔔 App came to foreground, checking notifications...');

    // Update last notification check time
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(CPref.lastNotificationCheck, DateTime.now().toIso8601String());

    // You can add logic here to fetch missed notifications
    // or update notification badge count
  }

  /// Handle app background - prepare for background notifications
  Future<void> onAppBackground() async {
    debugPrint('🔔 App went to background');

    // You can add logic here to handle background state
    // such as scheduling local notifications or updating server
  }

  /// Get notification statistics
  Future<Map<String, dynamic>> getNotificationStats() async {
    final prefs = await SharedPreferences.getInstance();

    return {
      'isRegistered': prefs.getBool(CPref.isTokenRegistered) ?? false,
      'isEnabled': prefs.getBool(CPref.notificationEnabled) ?? true,
      'fcmToken': prefs.getString(CPref.fcmToken),
      'lastCheck': prefs.getString(CPref.lastNotificationCheck),
      'accountId': prefs.getString(CPref.accountId),
      'isUserLoggedIn': _isUserLoggedIn,
    };
  }

  /// Check user login status from preferences
  Future<void> _checkUserLoginStatus() async {
    final prefs = await SharedPreferences.getInstance();
    final token = prefs.getString(CPref.token);
    final accountId = prefs.getString(CPref.accountId);

    _isUserLoggedIn = token != null && token.isNotEmpty && accountId != null && accountId.isNotEmpty;
    debugPrint('🔔 User login status: $_isUserLoggedIn');
  }

  /// Force refresh FCM token - useful for debugging
  Future<void> refreshFCMToken() async {
    debugPrint('🔔 Forcing FCM token refresh...');

    try {
      // This will trigger token refresh
      await _notificationService.getFCMToken();

      if (_isUserLoggedIn) {
        await _notificationService.registerFCMToken();
      }

      debugPrint('✅ FCM token refreshed successfully');
    } catch (e) {
      debugPrint('❌ Error refreshing FCM token: $e');
    }
  }

  /// Debug method to print current notification state
  Future<void> debugNotificationState() async {
    final stats = await getNotificationStats();
    debugPrint('🔔 === Notification Debug State ===');
    debugPrint('🔔 Is Registered: ${stats['isRegistered']}');
    debugPrint('🔔 Is Enabled: ${stats['isEnabled']}');
    debugPrint('🔔 FCM Token: ${stats['fcmToken']?.substring(0, 20) ?? 'null'}...');
    debugPrint('🔔 Account ID: ${stats['accountId']}');
    debugPrint('🔔 User Logged In: ${stats['isUserLoggedIn']}');
    debugPrint('🔔 Last Check: ${stats['lastCheck']}');
    debugPrint('🔔 Service Initialized: ${_notificationService.isInitialized}');
    debugPrint('🔔 ================================');
  }

  // Getters
  bool get isUserLoggedIn => _isUserLoggedIn;
  NotificationService get notificationService => _notificationService;
}
