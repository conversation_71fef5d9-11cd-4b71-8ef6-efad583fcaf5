import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/notification/widget/notifi_item.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../cubit/bloc.dart';
import '../../cubit/index.dart';
import '../../models/project/notification.dart';
import '../../utils/route_manager.dart';
import '../../utils/src/api.dart';
import '../../utils/src/convert_data.dart';
import '../../utils/src/dialogs.dart';
import '../Movie_schedule/model/banner_model.dart';

class NotificationPage extends StatefulWidget {
  const NotificationPage({super.key});

  @override
  State<NotificationPage> createState() => _NotificationPageState();
}

enum Option {
  first,
  second,
}

String str(Option option) {
  switch (option) {
    case Option.first:
      return 'Notification.MarkAllRead'.tr();
    case Option.second:
      return 'Notification.DeleteAll'.tr();
  }
}

class _NotificationPageState extends State<NotificationPage> {
  late final sNotification = RepositoryProvider.of<Api>(context).notification;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }
  /// Handle banner tap - tương tự iOS routeTapped()
  void _handleBannerTap(PromoModel promo) {
    // Use RouteManager to handle navigation
    final routeManager = RouteManager(
      context: context,
      type: RouteType.fromValue(promo.sreenCode),
      params: promo.refId.isNotEmpty ? [promo.refId] : [],
    );

    routeManager.route();
  }
  // Method để xử lý navigation theo screen code giống iOS


  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(title: 'Notification.Title'.tr(), titleColor: Colors.white, actions: [
        PopupMenuButton<Option>(
          child: Icon(
            Icons.more_horiz,
            color: CColor.black,
          ),
          itemBuilder: (context) {
            return [
              for (final option in Option.values)
                PopupMenuItem(
                  onTap: () {
                    if (option == Option.first) {
                      List<int> readIds = [];
                      for (var item in context.read<BlocC<MNotification>>().state.data.content) {
                        if (!item.isRead!) {
                          readIds.add(item.id!);
                        }
                      }
                      sNotification.markAsRead(body: {"notificationIdList": readIds}).then((response) {
                        if (response!.isSuccess) {
                          context.read<BlocC<MNotification>>().setPage(
                              page: 1,
                              format: MNotification.fromJson,
                              api: (filter, page, size, sort) =>
                                  sNotification.get(filter: filter, page: page, size: size));
                        }
                      });
                    } else {
                      UDialog().showConfirm(
                        title: "Notification.DeleteAllTitle".tr(),
                        body: Center(
                            child: Text(
                          "Notification.DeleteAllMessage".tr(),
                          textAlign: TextAlign.center,
                        )),
                        btnOkText: "Notification.Yes".tr(),
                        btnCancelText: "Notification.No".tr(),
                        btnCancelStyle: TextStyle(fontWeight: FontWeight.bold, color: CColor.blue),
                        btnOkOnPress: () async {
                          sNotification.delete().then((response) {
                            if (response!.isSuccess) {
                              context.pop();
                              context.read<BlocC<MNotification>>().setPage(
                                  page: 1,
                                  format: MNotification.fromJson,
                                  api: (filter, page, size, sort) =>
                                      sNotification.get(filter: filter, page: page, size: size));
                            }
                          });
                        },
                      );
                    }
                  },
                  value: option,
                  child: Text(str(option)),
                ),
            ];
          },
        ),
        const SizedBox(width: 20.0)
      ]),
      body: Padding(
          padding: const EdgeInsets.only(top: CSpace.base),
          child: WList<MNotification>(
            item: (data, int index) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  NotificationItem(
                      // Use simple mode by default (matches iOS NotificationCell.swift)
                      // Set isExpandable: true for expandable mode (matches iOS NotificationTableViewCell.swift)
                      isExpandable: true, // Change to true for expandable notifications
                      content: data.title, // Add content for expandable mode
                      onTap: () {
                        sNotification.markAsRead(body: {
                          "id": "${data.id}",
                          "ScreenCode": "${data.screenCode ?? 0}"
                        }).then((response) {
                          if (response!.isSuccess) {
                            context.read<BlocC<MNotification>>().setPage(
                                page: 1,
                                format: MNotification.fromJson,
                                api: (filter, page, size, sort) =>
                                    sNotification.get(filter: filter, page: page, size: size));
                          }

                          // Navigation logic giống iOS
                          if (data.screenCode == null || data.screenCode == 0) {
                            // Navigate to news detail
                            context.pushNamed(CRoute.newsDetail, extra: data);
                          } else {
                            _handleBannerTap(PromoModel(id: 0, sreenCode: data.screenCode ?? 0, name: 'name', imageUrl: 'imageUrl', refId: data.refId ?? ""));
                            // Navigate based on screen code
                            // _handleScreenCodeNavigation(context, data.screenCode!, data.refId);
                          }
                        });
                      },
                      notification: data,
                      onDelete: (_) => UDialog().showConfirm(
                            title: "Notification.DeleteTitle".tr(),
                            body: Center(
                                child: Text(
                              "Notification.DeleteMessage".tr(),
                              textAlign: TextAlign.center,
                            )),
                            btnOkText: "Notification.Yes".tr(),
                            btnCancelText: "Notification.No".tr(),
                            btnCancelStyle: TextStyle(fontWeight: FontWeight.bold, color: CColor.blue),
                            btnOkOnPress: () async {
                              sNotification.deleteById(id: data.id!).then((response) {
                                if (response!.isSuccess) {
                                  context.pop();
                                  context.read<BlocC<MNotification>>().setPage(
                                      page: 1,
                                      format: MNotification.fromJson,
                                      api: (filter, page, size, sort) =>
                                          sNotification.get(filter: filter, page: page, size: size));
                                }
                              });
                            },
                          ))
                ],
              );
            },
            api: (filter, page, size, sort) => sNotification.get(filter: filter, page: page, size: size),
            format: MNotification.fromJson,
          )),
    );
  }
}

class TestData {
  List<MNotification>? today;
  List<MNotification>? previous;

  TestData({this.today, this.previous});
}
