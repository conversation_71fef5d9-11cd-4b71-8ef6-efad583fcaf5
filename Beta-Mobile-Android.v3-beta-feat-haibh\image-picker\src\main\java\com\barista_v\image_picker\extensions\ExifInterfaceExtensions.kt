package com.barista_v.image_picker.extensions

import androidx.exifinterface.media.ExifInterface
import androidx.exifinterface.media.ExifInterface.ORIENTATION_UNDEFINED
import androidx.exifinterface.media.ExifInterface.TAG_ORIENTATION

val androidx.exifinterface.media.ExifInterface.orientation: Int
  get() = when (getAttributeInt(TAG_ORIENTATION, ORIENTATION_UNDEFINED)) {
    androidx.exifinterface.media.ExifInterface.ORIENTATION_ROTATE_90 -> 90
    androidx.exifinterface.media.ExifInterface.ORIENTATION_ROTATE_180 -> 180
    androidx.exifinterface.media.ExifInterface.ORIENTATION_ROTATE_270 -> 270
    else -> 0
  }