package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class VoucherPresenter : VoucherContractor.Presenter {

    private var disposable: Disposable? = null

    override fun getListVoucher(accountId: String, cardTypeName: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.getListVoucher().applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.showListVoucher(it)
                        }
                    }else {
                        it.Message?.let {
                            view?.get()?.showAlert(it)
                        }
                    }
                    view?.get()?.hideLoading()
                }, {
                    it.message?.let {
                        view?.get()?.showAlert(it)
                    }
                    view?.get()?.hideLoading()
                })
    }

    override fun registerVoucher(code: String, pin: String) {
        view?.get()?.showLoading()
        val mapData = mapOf("CustomerId" to (Global.share().user?.AccountId ?: ""),
                "CustomerCard" to (Global.share().user?.CardNumber ?: ""),
                "PinCode" to pin,
                "VoucherCode" to code,
                "CardTypeName" to "Voucher")
        disposable = APIClient.shared.accountAPI.registerVoucher(mapData).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.registerVoucherSuccess()
                            return@subscribe
                        }
                    }else {
                        it.Message?.let {
                            view?.get()?.showAlert(it)
                        }
                    }
                    view?.get()?.hideLoading()
                }, {
                    it.message?.let {
                        view?.get()?.showAlert(it)
                    }
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<VoucherContractor.View?>? = null
    override fun attachView(view: VoucherContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
