package vn.zenity.betacineplex.view.user

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_coupon.*
import kotlinx.android.synthetic.main.item_coupon_header.view.*
import kotlinx.android.synthetic.main.item_my_coupon.view.*
import showBarcode
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.toDate
import vn.zenity.betacineplex.helper.extension.toast
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

class CouponFragment : BaseFragment(), CouponContractor.View {
    override fun showListCoupon(items: List<VoucherModel>) {
        this.items = items.sortedByDescending { it.EndDate?.toDate(Constant.DateFormat.default) }
        activity?.runOnUiThread {
            (recyclerView?.adapter as? Adapter)?.notifyDataSetChanged()
        }
    }

    override fun registerCouponSuccess() {
        activity?.runOnUiThread {
            toast(getString(R.string.add_new_success))
        }
        presenter.getListCoupon(Global.share().user?.AccountId ?: return)
    }

    private val presenter = CouponPresenter()
    private var items: List<VoucherModel> = listOf()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_coupon
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView?.layoutManager = LinearLayoutManager(context)
        recyclerView?.adapter = Adapter()
        presenter.getListCoupon(Global.share().user?.AccountId ?: return)
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        private val TYPE_EVENT = 1
        private val TYPE_HEADER = 0

        override fun getItemCount(): Int {
            return items.size + 1
        }

        override fun getItemViewType(position: Int): Int {
            if (position == 0) return TYPE_HEADER
            return TYPE_EVENT
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (getItemViewType(position) == TYPE_HEADER) {
                holder.itemView.btnRegister.setOnClickListener {
                    val code = holder.itemView.edtCouponCode.text.toString().trim()
                    val pin = holder.itemView.edtCouponPin.text.toString().trim()
                    if (code.isEmpty() || pin.isEmpty()) {
                        showNotice("Mã Coupon và mã PIN không được bỏ trống")
                        return@setOnClickListener
                    }
                    presenter.registerCoupon(code, pin)
                }
            } else {
                val model = items[position - 1]
                holder.itemView.tvCardType.text = model.VoucherPackageName
                holder.itemView.tvCardNumber.text = model.VoucherCode
                holder.itemView.tvTimeExpire.text = model.EndDate?.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.dateSavis)
                holder.itemView.cardNumberBarcode.showBarcode(model.Voucher_Card_Id ?: "9999")
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            if (viewType == TYPE_HEADER) {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_coupon_header, parent, false)
                return Holder(itemView)
            } else {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_my_coupon, parent, false)
                return Holder(itemView)
            }
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
