<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="20dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHighlightContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        tools:text="Xác nhận tặng 50 điểm cho tài kho<PERSON>n"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHighlightUser"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:textColor="@color/black"
        android:textSize="18sp"
        app:fontFamily="@font/sanspro_bold"
        tools:text="9782185950" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvHighlightEmail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"
        tools:text="(<EMAIL>)" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnHighlightCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:fontFamily="@font/sanspro_bold"
            android:text="@string/cancel"
            android:textColor="@color/colorPrimaryDark" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/grayLine" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnHighlightOk"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:fontFamily="@font/sanspro_bold"
            android:text="@string/confirm"
            android:textColor="@color/colorPrimaryDark" />
    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="5dp" />
</LinearLayout>