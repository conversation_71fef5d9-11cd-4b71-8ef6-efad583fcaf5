import 'dart:async'; // ✅ Add for Timer
import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';

import '../../../core/constants/payment_constants.dart';
import '../../../cubit/index.dart';
import '../../Movie_schedule/model/Film_model.dart';
import '../../voucher/api/api_test.dart';
import '../model/cinema_model.dart';
import '../model/list_seat_model.dart';
import '../model/seat_model.dart';

/// Android Style WebView Payment - Exactly like Android repo
/// This implementation mirrors the Android BookingPaymentFragment approach:
/// 1. Call createBooking API to get HTML content (presenter.getBookingPayment)
/// 2. Load HTML with base URL (loadDataWithBaseURL)
/// 3. Execute getBookingInfo + getCustomerInfo on onPageFinished
/// 4. Use androidkit JavaScript interface (@JavascriptInterface)
/// 5. Proper error handling and navigation like Android
class AndroidStyleWebViewPayment extends StatefulWidget {
  final FilmModel? film;
  final String? combo;
  final ListSeatModel? listSeat;
  final String? cinemaId;
  final String? cinemaName;
  final Function(String?) onPaymentSuccess;
  final Function(String?) onPaymentFailed;
  final Function() onPaymentWaiting;
  final Function(String) onPaymentMethodSelected;
  final int? totalPrice;
  final List<SeatModel>? selectedSeats;
  final ShowModel? showTime;
  final String? showId;
  final int? remainingTime;        // ✅ Add timer parameter like iOS Style
  final DateTime? timeStartBooking; // ✅ Add start time parameter like iOS Style

  const AndroidStyleWebViewPayment({
    super.key,
    this.film,
    this.combo,
    this.listSeat,
    this.cinemaId,
    this.totalPrice,
    this.cinemaName,
    required this.onPaymentSuccess,
    required this.onPaymentFailed,
    required this.onPaymentWaiting,
    required this.onPaymentMethodSelected,
    this.selectedSeats,
    this.showTime,
    this.showId,
    this.remainingTime,        // ✅ Add to constructor
    this.timeStartBooking,     // ✅ Add to constructor
  });

  @override
  State<AndroidStyleWebViewPayment> createState() => _AndroidStyleWebViewPaymentState();
}

class _AndroidStyleWebViewPaymentState extends State<AndroidStyleWebViewPayment> {
  late InAppWebViewController webViewController;
  bool _isLoading = true;
  String? _htmlContent;
  String? _initialUrl;
  String? _currentUrl;

  // ✅ Timer variables exactly like iOS Style
  Timer? _countdownTimer;
  int _remainingSeconds = 600; // 10 minutes default

  // Deep link method channel for MoMo payment return exactly like Android repo
  static const MethodChannel _deepLinkChannel = MethodChannel('com.beta.betacineplex/deeplink');

  @override
  void initState() {
    super.initState();

    // ✅ Initialize timer from seat like iOS Style
    _remainingSeconds = widget.remainingTime ?? 600;

    // Validate customer data and log debug info
    _validateAndLogData();

    // Enable WebView debugging for Android (like Android repo)
    if (Platform.isAndroid) {
      InAppWebViewController.setWebContentsDebuggingEnabled(true);
    }

    // Setup deep link listener for MoMo payment return exactly like Android repo BroadcastReceiver
    _setupDeepLinkListener();

    // ✅ Start countdown timer like iOS Style
    _startCountdownTimer();

    // Call createBooking API to get HTML content (exactly like Android repo)
    _getBookingPayment();
  }

  @override
  void dispose() {
    _stopCountdownTimer(); // ✅ Stop timer like iOS Style
    _deepLinkChannel.setMethodCallHandler(null);
    super.dispose();
  }

  /// Start countdown timer exactly like iOS Style PaymentViewController
  void _startCountdownTimer() {
    _stopCountdownTimer();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        timer.cancel();
        _handleTimeout();
      }
    });
  }

  void _stopCountdownTimer() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
  }

  void _handleTimeout() {
    if (mounted) {
      print('⏰ Android Style: Timer expired, popping back to seat');
      widget.onPaymentFailed('Timeout: Session expired');
      Navigator.of(context).pop(); // ✅ Auto pop like iOS Style
    }
  }

  /// Setup deep link listener for payment returns exactly like Android repo BroadcastReceiver
  void _setupDeepLinkListener() {
    print('🔧 Android Style: Setting up deep link listener for payment returns');
    _deepLinkChannel.setMethodCallHandler((call) async {
      print('📞 Android Style: MethodChannel call received: ${call.method}');

      switch (call.method) {
        case 'onMoMoPaymentReturn':
          final arguments = call.arguments as Map<dynamic, dynamic>;
          print('💳 Android Style: MoMo payment return received via deep link!');
          print('📱 Arguments: $arguments');
          await _handleMoMoPaymentReturn(arguments);
          break;

        case 'onZaloPayPaymentReturn':
          final arguments = call.arguments as Map<dynamic, dynamic>;
          print('💳 Android Style: ZaloPay payment return received via deep link!');
          print('📱 Arguments: $arguments');
          await _handleZaloPayPaymentReturn(arguments);
          break;

        case 'onAirPayPaymentReturn':
          final arguments = call.arguments as Map<dynamic, dynamic>;
          print('💳 Android Style: AirPay payment return received via deep link!');
          print('📱 Arguments: $arguments');
          await _handleAirPayPaymentReturn(arguments);
          break;

        case 'onShopeePayPaymentReturn':
          final arguments = call.arguments as Map<dynamic, dynamic>;
          print('💳 Android Style: ShopeePay payment return received via deep link!');
          print('📱 Arguments: $arguments');
          await _handleShopeePayPaymentReturn(arguments);
          break;

        default:
          print('❓ Android Style: Unknown method call: ${call.method}');
          break;
      }
    });
  }

  /// Handle MoMo payment return exactly like Android repo BroadcastReceiver
  Future<void> _handleMoMoPaymentReturn(Map<dynamic, dynamic> arguments) async {
    try {
      print('🔧 Android Style: Processing MoMo payment return...');

      final orderId = arguments['orderId']?.toString() ?? '';
      final resultCode = arguments['resultCode']?.toString() ?? '';
      final requestId = arguments['requestId']?.toString() ?? '';
      final transId = arguments['transId']?.toString() ?? '';
      final message = arguments['message']?.toString() ?? '';
      final responseTime = arguments['responseTime']?.toString() ?? '';
      final payType = arguments['payType']?.toString() ?? '';
      final extraData = arguments['extraData']?.toString() ?? '';
      final partnerCode = arguments['partnerCode']?.toString() ?? '';

      print('💳 Android Style: MoMo parameters:');
      print('   - orderId: $orderId');
      print('   - resultCode: $resultCode');
      print('   - transId: $transId');

      // Call checkMomoTransactionStatus exactly like Android repo
      final jsCall = '''
        console.log('🔧 Android Style: Calling checkMomoTransactionStatus from deep link...');
        console.log('🔧 Android Style: MoMo parameters - orderId: $orderId, resultCode: $resultCode');

        // Check if function exists
        if (typeof checkMomoTransactionStatus === "function") {
          console.log('✅ Android Style: checkMomoTransactionStatus function found, calling...');
          try {
            checkMomoTransactionStatus(
              '$orderId',
              '$resultCode',
              '$requestId',
              '$transId',
              '$message',
              '$responseTime',
              '$payType',
              '$extraData',
              '$partnerCode'
            );
            console.log('✅ Android Style: checkMomoTransactionStatus called successfully');
          } catch (error) {
            console.error('❌ Android Style: Error calling checkMomoTransactionStatus:', error);
            // Fallback: directly call payment success if resultCode indicates success
            if ('$resultCode' === '0') {
              console.log('🔄 Android Style: Fallback - calling payment success directly');
              if (window.androidkit && window.androidkit.postMessage) {
                window.androidkit.postMessage('payment_success');
              }
            } else {
              console.log('🔄 Android Style: Fallback - calling payment failed directly');
              if (window.androidkit && window.androidkit.postMessage) {
                window.androidkit.postMessage('payment_failed');
              }
            }
          }
        } else {
          console.error('❌ Android Style: checkMomoTransactionStatus function not found');
          console.log('🔍 Android Style: Available functions:', Object.getOwnPropertyNames(window).filter(name => typeof window[name] === 'function'));

          // Fallback: directly handle payment result based on resultCode
          if ('$resultCode' === '0') {
            console.log('🔄 Android Style: Direct success - resultCode is 0');
            if (window.androidkit && window.androidkit.postMessage) {
              window.androidkit.postMessage('payment_success');
            }
          } else {
            console.log('🔄 Android Style: Direct failure - resultCode is not 0');
            if (window.androidkit && window.androidkit.postMessage) {
              window.androidkit.postMessage('payment_failed');
            }
          }
        }
      ''';

      await _loadJs(jsCall);
      print('✅ Android Style: MoMo payment return processed successfully');

      // Add timeout fallback - if no response after 10 seconds, handle directly
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          print('⏰ Android Style: MoMo payment timeout, checking resultCode directly');
          if (resultCode == '0') {
            print('✅ Android Style: MoMo payment success (timeout fallback)');
            widget.onPaymentSuccess('MoMo payment completed');
          } else {
            print('❌ Android Style: MoMo payment failed (timeout fallback)');
            widget.onPaymentFailed('MoMo payment failed: $message');
          }
        }
      });
    } catch (e) {
      print('❌ Android Style: Error handling MoMo payment return: $e');
      // Handle error case
      if (arguments['resultCode']?.toString() == '0') {
        widget.onPaymentSuccess('MoMo payment completed');
      } else {
        widget.onPaymentFailed('MoMo payment error: $e');
      }
    }
  }

  /// Handle ZaloPay payment return exactly like Android repo
  Future<void> _handleZaloPayPaymentReturn(Map<dynamic, dynamic> arguments) async {
    try {
      print('🔄 Android Style: Processing ZaloPay payment return...');

      final appTransId = arguments['appTransId']?.toString() ?? '';
      final status = arguments['status']?.toString() ?? '';
      final amount = arguments['amount']?.toString() ?? '';

      print('📱 Android Style: ZaloPay return parameters:');
      print('   - appTransId: $appTransId');
      print('   - status: $status');
      print('   - amount: $amount');

      // Process ZaloPay return exactly like Android repo
      final jsCall = '''
        if (typeof processZaloPayReturn === 'function') {
          processZaloPayReturn('$appTransId', '$status', '$amount');
        } else {
          console.log('⚠️ processZaloPayReturn function not found');
        }
      ''';

      await _loadJs(jsCall);
      print('✅ Android Style: ZaloPay payment return processed successfully');

      // Add timeout fallback - if no response after 10 seconds, handle directly
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          print('⏰ Android Style: ZaloPay payment timeout, checking status directly');
          if (status == '1' || status == 'success') {
            print('✅ Android Style: ZaloPay payment success (timeout fallback)');
            widget.onPaymentSuccess('ZaloPay payment completed');
          } else {
            print('❌ Android Style: ZaloPay payment failed (timeout fallback)');
            widget.onPaymentFailed('ZaloPay payment failed: status $status');
          }
        }
      });
    } catch (e) {
      print('❌ Android Style: Error handling ZaloPay payment return: $e');
      // Handle error case
      if (arguments['status']?.toString() == '1') {
        widget.onPaymentSuccess('ZaloPay payment completed');
      } else {
        widget.onPaymentFailed('ZaloPay payment error: $e');
      }
    }
  }

  /// Handle AirPay payment return exactly like Android repo
  Future<void> _handleAirPayPaymentReturn(Map<dynamic, dynamic> arguments) async {
    try {
      print('🔄 Android Style: Processing AirPay payment return...');

      final orderId = arguments['orderId']?.toString() ?? '';
      final status = arguments['status']?.toString() ?? '';

      print('📱 Android Style: AirPay return parameters:');
      print('   - orderId: $orderId');
      print('   - status: $status');

      // Process AirPay return exactly like Android repo
      final jsCall = '''
        if (typeof processAirPayReturn === 'function') {
          processAirPayReturn('$orderId', '$status');
        } else {
          console.log('⚠️ processAirPayReturn function not found');
        }
      ''';

      await _loadJs(jsCall);
      print('✅ Android Style: AirPay payment return processed successfully');

      // Add timeout fallback - if no response after 10 seconds, handle directly
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          print('⏰ Android Style: AirPay payment timeout, checking status directly');
          if (status == '1' || status == 'success') {
            print('✅ Android Style: AirPay payment success (timeout fallback)');
            widget.onPaymentSuccess('AirPay payment completed');
          } else {
            print('❌ Android Style: AirPay payment failed (timeout fallback)');
            widget.onPaymentFailed('AirPay payment failed: status $status');
          }
        }
      });
    } catch (e) {
      print('❌ Android Style: Error handling AirPay payment return: $e');
      // Handle error case
      if (arguments['status']?.toString() == '1') {
        widget.onPaymentSuccess('AirPay payment completed');
      } else {
        widget.onPaymentFailed('AirPay payment error: $e');
      }
    }
  }

  /// Handle ShopeePay payment return exactly like Android repo
  Future<void> _handleShopeePayPaymentReturn(Map<dynamic, dynamic> arguments) async {
    try {
      print('🔄 Android Style: Processing ShopeePay payment return...');

      final orderId = arguments['orderId']?.toString() ?? '';
      final status = arguments['status']?.toString() ?? '';
      final amount = arguments['amount']?.toString() ?? '';

      print('📱 Android Style: ShopeePay return parameters:');
      print('   - orderId: $orderId');
      print('   - status: $status');
      print('   - amount: $amount');

      // Process ShopeePay return exactly like Android repo
      final jsCall = '''
        if (typeof processShopeePayReturn === 'function') {
          processShopeePayReturn('$orderId', '$status', '$amount');
        } else {
          console.log('⚠️ processShopeePayReturn function not found');
        }
      ''';

      await _loadJs(jsCall);
      print('✅ Android Style: ShopeePay payment return processed successfully');

      // Add timeout fallback - if no response after 10 seconds, handle directly
      Future.delayed(const Duration(seconds: 10), () {
        if (mounted) {
          print('⏰ Android Style: ShopeePay payment timeout, checking status directly');
          if (status == '1' || status == 'success') {
            print('✅ Android Style: ShopeePay payment success (timeout fallback)');
            widget.onPaymentSuccess('ShopeePay payment completed');
          } else {
            print('❌ Android Style: ShopeePay payment failed (timeout fallback)');
            widget.onPaymentFailed('ShopeePay payment failed: status $status');
          }
        }
      });
    } catch (e) {
      print('❌ Android Style: Error handling ShopeePay payment return: $e');
      // Handle error case
      if (arguments['status']?.toString() == '1') {
        widget.onPaymentSuccess('ShopeePay payment completed');
      } else {
        widget.onPaymentFailed('ShopeePay payment error: $e');
      }
    }
  }

  /// Validate customer data and log all payment data for debugging
  void _validateAndLogData() {
    print('🔧 ===== ANDROID STYLE WEBVIEW PAYMENT DEBUG =====');

    // Validate and log customer data
    final user = context.read<AuthC>().state.user;
    print('👤 Customer Data Validation:');
    print('   - User object: ${user != null ? "✅ Available" : "❌ NULL"}');
    print('   - Account ID: ${user?.accountId ?? "❌ EMPTY"}');
    print('   - Card Number: ${user?.cardNumber ?? "❌ EMPTY"}');
    print('   - User Name: ${user?.name ?? "❌ EMPTY"}');
    print('   - Phone: ${user?.phoneNumber ?? "❌ EMPTY"}');
    print('   - Email: ${user?.email ?? "❌ EMPTY"}');

    // Critical validation
    if (user?.accountId?.isEmpty ?? true) {
      print('❌ CRITICAL: Customer ID is empty - Payment will fail!');
    }
    if (user?.cardNumber?.isEmpty ?? true) {
      print('❌ CRITICAL: Customer card number is empty - Payment will fail!');
    }

    // Log booking data
    print('🎬 Booking Data:');
    print('   - Film: ${widget.film?.getName() ?? "❌ EMPTY"}');
    print('   - Cinema: ${widget.listSeat?.tenRap ?? "❌ EMPTY"}');
    print('   - Show ID: ${widget.showId ?? widget.showTime?.showId ?? "❌ EMPTY"}');
    print('   - Total Price: ${widget.totalPrice ?? "❌ EMPTY"}');
    print('   - Selected Seats: ${widget.selectedSeats?.length ?? 0}');
    print('   - Combo: ${widget.combo ?? "❌ EMPTY"}');

    // Log seat details
    if (widget.selectedSeats?.isNotEmpty ?? false) {
      print('🪑 Selected Seats Details:');
      for (var seat in widget.selectedSeats!) {
        print('   - Seat ${seat.seatNumber}: Index=${seat.seatIndex}, Type=${seat.seatTypeEnum}');
      }
    }

    // Log show time data
    print('⏰ Show Time Data:');
    print('   - Date: ${widget.listSeat?.ngayChieu ?? "❌ EMPTY"}');
    print('   - Time: ${widget.listSeat?.gioChieu ?? "❌ EMPTY"}');
    print('   - Screen: ${widget.listSeat?.phongChieu ?? "❌ EMPTY"}');
    print('   - Format: ${widget.listSeat?.filmFormatCode ?? "❌ EMPTY"}');

    print('🔧 ===== END ANDROID STYLE DEBUG =====');
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          final shouldPop = await _onWillPop();
          if (shouldPop && context.mounted) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: appBar(
          title: 'Thanh toán',
          titleColor: Colors.white,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios_new),
            onPressed: _handleBackButton,
          ),
          actions: [
            // ✅ Add timer display like iOS Style
            Center(
              child: Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                  decoration: BoxDecoration(
                    color: _remainingSeconds < 60 ? Colors.red.shade700 : Colors.green,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    _formatTime(_remainingSeconds),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 15,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      body: Stack(
        children: [
          if (_htmlContent != null)
            InAppWebView(
              // Load HTML content with base URL (exactly like Android repo)
              initialData: InAppWebViewInitialData(
                data: _htmlContent!,
                baseUrl: WebUri('${ApiService.baseUrl}/Booking'),
              ),
              initialSettings: InAppWebViewSettings(
                // Android repo WebView settings
                javaScriptEnabled: true,
                domStorageEnabled: true,
                useWideViewPort: true,
                loadWithOverviewMode: true,
                supportZoom: true,
                builtInZoomControls: false,
                displayZoomControls: false,

                // Security and compatibility
                mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
                allowsInlineMediaPlayback: true,
                mediaPlaybackRequiresUserGesture: false,
                // supportMultipleWindows: false,

                // Performance
                cacheEnabled: true,
                clearCache: false,
              ),
              onWebViewCreated: (controller) async {
                webViewController = controller;

                // Add JavaScript interface "androidkit" (exactly like Android repo)
                controller.addJavaScriptHandler(
                  handlerName: "androidkit",
                  callback: (args) {
                    if (!mounted) return;
                    final message = args.isNotEmpty ? args[0].toString() : '';
                    print('📱 Android Style WebView received message: $message');
                    _handleJavaScriptMessage(message);
                  }
                );

                // Add postPaymentSuccess handler exactly like Android repo @JavascriptInterface
                controller.addJavaScriptHandler(
                  handlerName: "postPaymentSuccess",
                  callback: (args) {
                    if (!mounted) return;
                    final transactionId = args.isNotEmpty ? args[0].toString() : '';
                    print('✅ Android Style: postPaymentSuccess called with transaction ID: $transactionId');
                    _handlePaymentSuccess(transactionId);
                  }
                );

                print('✅ Android Style WebView JavaScript handlers added: androidkit, postPaymentSuccess');
              },
              onLoadStart: (controller, url) {
                print('🔄 Android Style WebView started loading: $url');

                // Track URL loading for navigation
                _updateCurrentUrl(url.toString());
              },
              onProgressChanged: (controller, progress) {
                print('📊 Android Style WebView loading progress: $progress%');
              },
              onLoadStop: (controller, url) async {
                print('✅ Android Style WebView page loaded: $url');

                // Track URL changes for navigation handling
                _updateCurrentUrl(url.toString());

                setState(() {
                  _isLoading = false;
                });

                // Inject JavaScript to make postPaymentSuccess available on androidkit object
                await _injectAndroidKitInterface();

                // Special handling for OnePay result pages
                if (url.toString().contains('ketquathanhtoan')) {
                  print('🔄 Android Style: OnePay result page detected, processing payment result');
                  _handlePaymentResult(url.toString());
                  return; // Don't execute getBookingInfo for result pages
                }

                // Execute getBookingInfo with delay exactly like Android repo (200ms delay in onReceivedTitle)
                Future.delayed(const Duration(milliseconds: 200), () async {
                  if (mounted) {
                    await _executeGetBookingInfoWithScreenTypeCheck();
                  }
                });
              },
              onConsoleMessage: (controller, consoleMessage) {
                print('🖥️ Android Style Console [${consoleMessage.messageLevel}]: ${consoleMessage.message}');

                final level = consoleMessage.messageLevel;
                final message = consoleMessage.message;

                String emoji = '📱';
                switch (consoleMessage.messageLevel) {
                  case ConsoleMessageLevel.ERROR:
                    emoji = '❌';
                    break;
                  case ConsoleMessageLevel.WARNING:
                    emoji = '⚠️';
                    break;
                  case ConsoleMessageLevel.LOG:
                    emoji = 'ℹ️';
                    break;
                  default:
                    emoji = '📱';
                }

                print('$emoji Android Style JS Console [$level]: $message');
              },
              onJsAlert: (controller, jsAlertRequest) async {
                final message = jsAlertRequest.message ?? '';
                print('🚨 Android Style JS Alert: $message');

                return JsAlertResponse(
                  handledByClient: false,
                  action: JsAlertResponseAction.CONFIRM,
                );
              },
              onJsConfirm: (controller, jsConfirmRequest) async {
                final message = jsConfirmRequest.message ?? '';
                print('❓ Android Style JS Confirm: $message');

                return JsConfirmResponse(
                  handledByClient: false,
                  action: JsConfirmResponseAction.CONFIRM,
                );
              },
              // Add URL navigation handling exactly like Android repo
              shouldOverrideUrlLoading: (controller, navigationAction) async {
                final url = navigationAction.request.url?.toString() ?? '';
                print('🔗 Android Style: URL navigation request: $url');

                // Handle OnePay payment result URLs - CRITICAL for OnePay callback
                // if (url.contains('ketquathanhtoan')) {
                //   print('📄 Android Style: OnePay payment result URL detected - processing payment result');
                //   _handlePaymentResult(url);
                //   return NavigationActionPolicy.CANCEL; // Block navigation but process result
                // }

                // Block navigation to payment success URLs that cause "Page Not Found"
                if (url.contains('/booking/thanhtoan') ||
                    url.contains('/booking/thanhtoanzalopay') ||
                    url.contains('/booking/thanhtoanmomo') ||
                    url.contains('/booking/thanhtoanshopeepay') ||
                    url.contains('/booking/thanhtoanshopeepayonline')) {
                  print('🚫 Android Style: Blocking navigation to payment success URL: $url');
                  return NavigationActionPolicy.CANCEL;
                }

                // Handle payment method redirects exactly like Android repo
                return await _handleUrlNavigation(url);
              },
            ),

          // Loading indicator
          if (_isLoading)
            const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.red),
                  SizedBox(height: 16),
                  Text(
                    'Loading payment page...',
                    style: TextStyle(fontSize: 16, color: Colors.grey),
                  ),
                ],
              ),
            ),
        ],
      ),
      ),
    );
  }

  /// Call createBooking API to get HTML content (exactly like Android repo)
  Future<void> _getBookingPayment() async {
    try {
      print('🔧 Android Style: Calling createBooking API...');
      setState(() {
        _isLoading = true;
      });

      // Create booking model (similar to Android repo CreateBookingModel)
      final bookingModel = _createBookingModel();

      // Get user data for headers
      final user = context.read<AuthC>().state.user;
      final token = await ApiService.getToken();

      print('🔧 Android Style: Preparing API headers...');
      print('   - User ID: ${user?.accountId ?? "❌ EMPTY"}');
      print('   - Token: ${token?.isNotEmpty == true ? "✅ Available" : "❌ EMPTY"}');

      // Validate required headers
      if (user?.accountId?.isEmpty ?? true) {
        throw Exception('User ID is required for booking API');
      }
      if (token?.isEmpty ?? true) {
        throw Exception('Authorization token is required for booking API');
      }

      // Call API to get HTML content with required headers
      final response = await http.post(
        Uri.parse('${ApiService.baseUrl}/Booking'),
        headers: {

          'Content-Type': 'application/json',
          'Accept': 'text/html',
          'X-User': user!.accountId!,           // ✅ User ID header
          'Authorization': 'Bearer $token',     // ✅ Authorization header
        },
        body: jsonEncode(bookingModel),
      );

      if (response.statusCode == 200) {
        setState(() {
          _htmlContent = response.body;
        });
        print('✅ Android Style: HTML content received from server');
      } else {
        print('❌ Android Style: API call failed with status ${response.statusCode}');
        _showError('Failed to load payment page');
      }
    } catch (e) {
      print('❌ Android Style: Error calling createBooking API: $e');
      _showError('Network error: $e');
    }
  }

  /// Create booking model for API call (similar to Android repo)
  Map<String, dynamic> _createBookingModel() {
    final selectedSeats = widget.selectedSeats ?? [];

    return {
      'ShowId': widget.showId ?? widget.showTime?.showId ?? widget.listSeat?.filmId,
      'Seats': selectedSeats.map((seat) => {
        'SeatIndex': seat.seatIndex ?? 0,
        'SeatName': seat.seatNumber ?? '',
        'SeatType': _getSeatType(seat),
        'TicketTypeId': _getTicketTypeId(seat),
        'Price': _getSeatPrice(seat),
      }).toList(),
      'CountDown': _calculateCountDown(), // ✅ Use exact calculation like iOS Style
      'ImageHost': ApiService.baseUrlImage,
      'TotalMoney': widget.totalPrice ?? 0,
      'FilmFormatCode': widget.listSeat?.filmFormatCode ?? '2d',
    };
  }

  /// Calculate countdown exactly like iOS Style
  String _calculateCountDown() {
    final expiredTime = widget.timeStartBooking?.add(const Duration(minutes: 10)) ??
                      DateTime.now().add(const Duration(minutes: 10));
    return '/Date(${expiredTime.millisecondsSinceEpoch})/';
  }

  String _getSeatType(SeatModel seat) {
    if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
      return 'VIP';
    } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
      return 'DOUBLE';
    } else {
      return 'STARDAR';
    }
  }

  String _getTicketTypeId(SeatModel seat) {
    final listSeat = widget.listSeat;
    if (listSeat?.ticketTypes == null) return '';

    if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
      return listSeat!.ticketTypes!.firstWhere((t) => t.isVip == true, orElse: () => listSeat.ticketTypes!.first).ticketTypeId ?? '';
    } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
      return listSeat!.ticketTypes!.firstWhere((t) => t.isCouple == true, orElse: () => listSeat.ticketTypes!.first).ticketTypeId ?? '';
    } else {
      return listSeat!.ticketTypes!.firstWhere((t) => t.isNormal == true, orElse: () => listSeat.ticketTypes!.first).ticketTypeId ?? '';
    }
  }

  int _getSeatPrice(SeatModel seat) {
    final listSeat = widget.listSeat;
    if (listSeat?.ticketTypes == null) return 0;

    if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
      return listSeat!.ticketTypes!.firstWhere((t) => t.isVip == true, orElse: () => listSeat.ticketTypes!.first).price ?? 0;
    } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
      return listSeat!.ticketTypes!.firstWhere((t) => t.isCouple == true, orElse: () => listSeat.ticketTypes!.first).price ?? 0;
    } else {
      return listSeat!.ticketTypes!.firstWhere((t) => t.isNormal == true, orElse: () => listSeat.ticketTypes!.first).price ?? 0;
    }
  }

  /// Execute getBookingInfo with screen type check exactly like Android repo
  Future<void> _executeGetBookingInfoWithScreenTypeCheck() async {
    try {
      print('🔧 Android Style: Checking screen type before executing getBookingInfo...');

      // Get current screen type exactly like Android repo getCurrentType()
      final screenType = await webViewController.evaluateJavascript(source: JavaScriptMethods.screenType);
      final screenTypeStr = screenType?.toString().replaceAll('"', '') ?? ScreenTypes.payment;

      print('🔍 Android Style: Current screen type: $screenTypeStr');

      // Execute getBookingInfo based on screen type exactly like Android repo
      switch (screenTypeStr) {
        case ScreenTypes.voucher:
        case ScreenTypes.coupon:
        case ScreenTypes.betaPoint:
          print('🔄 Android Style: Special screen detected, not executing getBookingInfo');
          break;

        case ScreenTypes.payment:
        default:
          print('💳 Android Style: Payment screen detected, executing getBookingInfo');
          await _executeGetBookingInfo();
          break;
      }
    } catch (e) {
      print('❌ Android Style: Error in screen type check: $e');
      // Fallback to executing getBookingInfo anyway
      await _executeGetBookingInfo();
    }
  }

  /// Execute getBookingInfo immediately on page finished (exactly like Android repo)
  Future<void> _executeGetBookingInfo() async {
    try {
      print('🔧 Android Style: Executing getBookingInfo...');

      // Initialize dataBooking object first (exactly like Android repo)
      final jsDataBooking = _buildDataBookingJS();
      await _loadJs(jsDataBooking);

      // Initialize global variables (exactly like Android repo)
      const jsInitGlobals = '''
        if (typeof bookingInfor === 'undefined') { var bookingInfor = {}; }
        if (typeof paymentInfor === 'undefined') { var paymentInfor = {}; }
        bookingInfor.seats = dataBooking.Seats;
        bookingInfor.ShowId = dataBooking.ShowId;
        console.log('📋 Android Style: bookingInfor initialized:', bookingInfor);
      ''';
      await _loadJs(jsInitGlobals);

      // Build JavaScript exactly like Android repo
      final jsGetBookingInfo = _buildGetBookingInfoJS();
      await _loadJs(jsGetBookingInfo);

      // Set customer info exactly like Android repo
      final jsCustomerInfo = _buildCustomerInfoJS();
      await _loadJs(jsCustomerInfo);

      print('✅ Android Style: getBookingInfo executed successfully');
    } catch (e) {
      print('❌ Android Style: Error executing getBookingInfo: $e');
    }
  }

  /// Build dataBooking JavaScript exactly like Android repo
  String _buildDataBookingJS() {
    final showId = widget.showId ??  widget.showTime?.showId ?? '';

    // Build seats array
    final seats = widget.selectedSeats?.map((seat) {
      return '''{
        "SeatIndex": ${seat.seatIndex ?? 0},
        "SeatName": "${_escapeString(seat.seatNumber ?? "")}",
        "SeatType": "${_escapeString(_getSeatType(seat))}",
        "TicketTypeId": "${_escapeString(_getTicketTypeId(seat))}",
        "Price": ${_getSeatPrice(seat)}
      }''';
    }).join(',') ?? '';

    // Build dataBooking object
    final jsDataBooking = '''
      var dataBooking = {
        "ShowId": "$showId",
        "Seats": [$seats],
        "CountDown": "/Date(${DateTime.now().millisecondsSinceEpoch + 600000})/",
        "ImageHost": "${ApiService.baseUrlImage}"
      };
      console.log('📊 Android Style: dataBooking initialized:', dataBooking);
    ''';

    return jsDataBooking;
  }

  /// Build getBookingInfo JavaScript (exactly like Android repo format)
  String _buildGetBookingInfoJS() {
    final film = widget.film;
    final listSeat = widget.listSeat;

    // Format date and time exactly like Android repo
    final dateFormat = DateFormat('dd/MM/yyyy');
    final timeFormat = DateFormat('HH:mm');

    DateTime? date;
    DateTime? time;

    try {
      if (listSeat?.ngayChieu != null) {
        date = DateTime.parse(listSeat!.ngayChieu!);
      }
      if (listSeat?.gioChieu != null) {
        time =  DateTime.parse(listSeat!.gioChieu!); // parse toàn bộ
      }
    } catch (e) {
      print('⚠️ Android Style: Error parsing date/time: $e');
    }

    final dateStr = date != null ? dateFormat.format(date) : '';
    final timeStr = time != null ? timeFormat.format(time) : '';

    // Build film info exactly like Android repo
    final filmInfo = '${listSeat?.filmFormatName ?? ''} | ${film?.getFilmGenre() ?? ''} | ${film?.Duration ?? 0} phút';

    // Build JavaScript exactly like Android repo (string concatenation style)
    final jsGetBookingInfo = '''
var bookingIf = {};
bookingIf.FilmName = "${_escapeString(film?.getName() ?? "")}";
bookingIf.FilmInfo = "${_escapeString(filmInfo)}";
bookingIf.CinemaName = "${_escapeString(listSeat?.tenRap ?? "")}";
bookingIf.DateShow = "$dateStr";
bookingIf.ShowTime = "$timeStr";
bookingIf.Combo = "${_escapeString(widget.combo ?? "")}";
bookingIf.TotalMoney = ${widget.totalPrice ?? 0};
bookingIf.Screen = "${_escapeString(listSeat?.phongChieu ?? "")}";
bookingIf.FilmPoster = "${ApiService.baseUrlImage}${film?.MainPosterUrl ?? ""}";
getBookingInfo(bookingIf);
'''.trim();

    return jsGetBookingInfo;
  }

  /// Build customer info JavaScript (exactly like Android repo format)
  String _buildCustomerInfoJS() {
    final user = context.read<AuthC>().state.user;

    // Validate customer data before building JavaScript
    final customerId = user?.accountId ?? "";
    final customerCard = user?.cardNumber ?? "";

    print('🔧 Building customer info JavaScript:');
    print('   - Customer ID: ${customerId.isEmpty ? "❌ EMPTY" : "✅ $customerId"}');
    print('   - Customer Card: ${customerCard.isEmpty ? "❌ EMPTY" : "✅ $customerCard"}');

    if (customerId.isEmpty) {
      print('❌ WARNING: Customer ID is empty - Payment may fail!');
    }
    if (customerCard.isEmpty) {
      print('❌ WARNING: Customer card is empty - Payment may fail!');
    }

    // Build JavaScript exactly like Android repo (string concatenation style)
    final jsCustomerInfo = '''
console.log("🔧 Setting customer info...");
console.log("👤 Customer ID: $customerId");
console.log("💳 Customer Card: $customerCard");
var cusI = {};
cusI.customerId = "$customerId";
cusI.customerCard = "$customerCard";
console.log("📱 Calling getCustomerInfo with:", cusI);
if (typeof getCustomerInfo === "function") {
  getCustomerInfo(cusI);
  console.log("✅ getCustomerInfo called successfully");
} else {
  console.error("❌ getCustomerInfo function not found");
}
'''.trim();

    return jsCustomerInfo;
  }

  /// Inject androidkit interface - simple like Android repo
  Future<void> _injectAndroidKitInterface() async {
    try {
      print('🔧 Android Style: Injecting androidkit interface...');

      // Simple androidkit interface like Android @JavascriptInterface
      const jsInterface = '''
        // Initialize screenType variable exactly like android_webview_payment.dart
        if (typeof screenType === 'undefined') {
          window.screenType = "payment";
        }

        // Initialize other required variables
        if (typeof isBooking === 'undefined') {
          window.isBooking = false;
        }
        if (typeof listCombo === 'undefined') {
          window.listCombo = [];
        }

        window.androidkit = {
          postMessage: function(message) {
            window.flutter_inappwebview.callHandler('androidkit', message);
          },
          postPaymentSuccess: function(transactionId) {
            window.flutter_inappwebview.callHandler('postPaymentSuccess', transactionId);
            return true;
          }
        };


        // Simple getTransactionId function like Android repo
        window.getTransactionId = function() {
          if (window.paymentInfor && window.paymentInfor.transactionId) {
            return window.paymentInfor.transactionId;
          }
          if (window.bookingInfor && window.bookingInfor.transactionId) {
            return window.bookingInfor.transactionId;
          }
          return null;
        };

        // Simple payment return functions like Android repo
        window.processZaloPayReturn = function(appTransId, status, amount) {
          if (status === '1' || status === 'success') {
            window.androidkit.postPaymentSuccess(appTransId);
          } else {
            window.androidkit.postMessage('payment_failed');
          }
        };

        window.processAirPayReturn = function(orderId, status) {
          if (status === '1' || status === 'success') {
            window.androidkit.postPaymentSuccess(orderId);
          } else {
            window.androidkit.postMessage('payment_failed');
          }
        };

        window.processShopeePayReturn = function(orderId, status, amount) {
          if (status === '1' || status === 'success') {
            window.androidkit.postPaymentSuccess(orderId);
          } else {
            window.androidkit.postMessage('payment_failed');
          }
        };

        // OnePay payment result handler for ketquathanhtoan callback
        window.processOnePayReturn = function(transactionId, responseCode) {
          console.log('🔄 OnePay payment result:', transactionId, responseCode);
          if (responseCode === '0' || responseCode === 0) {
            console.log('✅ OnePay payment successful, calling postPaymentSuccess');
            window.androidkit.postPaymentSuccess(transactionId || 'onepay_success');
          } else {
            console.log('❌ OnePay payment failed with code:', responseCode);
            window.androidkit.postMessage('payment_failed');
          }
        };

        // Function to navigate back to main page after payment success
        window.backToMain = function() {
          console.log('🔙 Navigating back to main page');
          window.androidkit.postMessage('back_to_main');
        };

        // Simple transaction status check functions like Android repo
        window.checkMomoTransactionStatus = function(orderId, resultCode, requestId, transId, message, responseTime, payType, extraData, partnerCode) {
          if (resultCode === '0') {
            window.androidkit.postPaymentSuccess(transId || orderId);
          } else {
            window.androidkit.postMessage('payment_failed');
          }
        };

        window.checkZaloPayTransactionStatus = function(appId, appTransId, pmcId, bankCode, amount, dAmount, status, checkSum) {
          if (status === '1' || status === 'success') {
            window.androidkit.postPaymentSuccess(appTransId);
          } else {
            window.androidkit.postMessage('payment_failed');
          }
        };

        window.checkShopeePayTransactionStatus = function(referenceId) {
          window.androidkit.postPaymentSuccess(referenceId);
        };
      ''';

      await webViewController.evaluateJavascript(source: jsInterface);
      print('✅ Android Style: androidkit interface injected successfully');

      // Test JavaScript interface availability
      // await _testJavaScriptInterface();
    } catch (e) {
      print('❌ Android Style: Error injecting androidkit interface: $e');
    }
  }

  /// Load JavaScript (exactly like Android repo loadJs method)
  Future<void> _loadJs(String js) async {
    try {
      print('🔧 Android Style: Executing JavaScript...');
      print('📝 JS Content: ${js.length > 100 ? "${js.substring(0, 100)}..." : js}');

      final result = await webViewController.evaluateJavascript(source: js);

      print('✅ Android Style: JavaScript executed successfully');
      if (result != null) {
        print('📤 JS Result: $result');
      }
    } catch (e) {
      print('❌ Android Style: Error executing JavaScript: $e');
      print('📝 Failed JS: ${js.length > 200 ? js.substring(0, 200) + "..." : js}');
    }
  }

  /// Handle payment success with transaction ID exactly like Android repo postPaymentSuccess
  void _handlePaymentSuccess(String transactionId) {
    print('✅ Android Style: Payment success with transaction ID: $transactionId');

    // Clean transaction ID (remove quotes if any)
    final cleanTransactionId = transactionId.replaceAll('"', '');

    // Prevent any further WebView navigation
    _preventFurtherNavigation();

    // Call onPaymentSuccess callback directly - notification will be handled by parent
    widget.onPaymentSuccess(cleanTransactionId.isNotEmpty ? cleanTransactionId : null);
  }

  /// Prevent further WebView navigation after payment success
  void _preventFurtherNavigation() {
    try {
      // Inject JavaScript to prevent any further navigation
      webViewController.evaluateJavascript(source: '''
        console.log('🚫 Android Style: Preventing further navigation');

        // Override all navigation methods
        window.location.assign = function(url) {
          console.log('🚫 Prevented location.assign to:', url);
        };

        window.location.replace = function(url) {
          console.log('🚫 Prevented location.replace to:', url);
        };

        // Disable all forms and links
        document.querySelectorAll('a, form').forEach(function(element) {
          element.style.pointerEvents = 'none';
        });

        console.log('✅ Android Style: Navigation prevention activated');
      ''');
    } catch (e) {
      print('❌ Android Style: Error preventing navigation: $e');
    }
  }

  /// Get transaction ID exactly like Android repo
  Future<String?> _getTransactionId() async {
    try {
      print('🔍 Android Style: Getting transaction ID...');

      final result = await webViewController.evaluateJavascript(
        source: 'getTransactionId();'
      );

      // Clean result (remove quotes)
      final transactionId = result?.toString().replaceAll('"', '');

      print('🔍 Android Style: Transaction ID result: $transactionId');

      if (transactionId != null && transactionId.isNotEmpty && transactionId != 'null') {
        return transactionId;
      }

      return null;
    } catch (e) {
      print('❌ Android Style: Error getting transaction ID: $e');
      return null;
    }
  }

  /// Handle JavaScript messages (exactly like Android repo @JavascriptInterface methods)
  void _handleJavaScriptMessage(String message) {
    print('📱 Android Style: Handling message: $message');

    final cleanMessage = message.replaceAll('"', '');

    switch (cleanMessage) {
      case 'payment_success':
        print('✅ Android Style: Payment success (without transaction ID)');
        _preventFurtherNavigation();
        // Call onPaymentSuccess callback directly - notification will be handled by parent
        widget.onPaymentSuccess(null);
        break;

      case 'awaiting_payment':
        print('⏳ Android Style: Payment awaiting');
        widget.onPaymentWaiting();
        break;

      case 'payment_failed':
        print('❌ Android Style: Payment failed');
        widget.onPaymentFailed(message);
        // Navigator.of(context).popUntil((route) => route.isFirst);
        break;

      case 'policy':
        print('📋 Android Style: Policy requested');
        // Handle policy page - could open terms of service
        break;

      case 'timeout':
        print('⏰ Android Style: Timeout message from JavaScript');
        _handleTimeout();
        break;

      case 'back_to_main':
        print('🔙 Android Style: Back to main requested from JavaScript');
        // Navigate back to main page - this will trigger onPaymentSuccess
        widget.onPaymentSuccess(null);
        break;

      case 'test_interface':
        print('🧪 Android Style: Test interface message received - JavaScript interface is working!');
        break;

      default:
        print('🔍 Android Style: Unknown message: $cleanMessage');
        // Handle other payment method selections
        if (cleanMessage.contains('payment_method')) {
          widget.onPaymentMethodSelected(cleanMessage);
        }
        break;
    }
  }

  /// Test JavaScript interface availability
  // Future<void> _testJavaScriptInterface() async {
  //   try {
  //     print('🧪 Testing JavaScript interface...');
  //
  //     const testJS = '''
  //       console.log('🧪 Testing androidkit interface...');
  //       console.log('🔍 window.androidkit exists:', !!window.androidkit);
  //       console.log('🔍 window.androidkit.postMessage exists:', !!(window.androidkit && window.androidkit.postMessage));
  //       console.log('🔍 window.androidkit.postPaymentSuccess exists:', !!(window.androidkit && window.androidkit.postPaymentSuccess));
  //
  //       // Test sending a test message
  //       if (window.androidkit && window.androidkit.postMessage) {
  //         console.log('🧪 Sending test message...');
  //         window.androidkit.postMessage('test_interface');
  //       }
  //
  //       // Add test functions for manual testing
  //       window.testPolicy = function() {
  //         console.log('🧪 Testing policy function...');
  //         if (window.androidkit) {
  //           window.androidkit.postMessage("policy");
  //         }
  //       };
  //
  //       window.testTimeout = function() {
  //         console.log('🧪 Testing timeout function...');
  //         if (window.androidkit) {
  //           window.androidkit.postMessage("timeout");
  //         }
  //       };
  //
  //       // Comprehensive test function
  //       window.runAllTests = function() {
  //         console.log('🧪 Running all interface tests...');
  //
  //         // Test 1: Interface availability
  //         console.log('Test 1: Interface availability');
  //         console.log('✅ androidkit exists:', !!window.androidkit);
  //         console.log('✅ postMessage exists:', !!(window.androidkit && window.androidkit.postMessage));
  //
  //         // Test 2: Message sending
  //         console.log('Test 2: Message sending');
  //         if (window.androidkit) {
  //           window.androidkit.postMessage('test_interface');
  //           console.log('✅ Test message sent');
  //         }
  //
  //         // Test 3: Server functions
  //         console.log('Test 3: Server functions');
  //         console.log('✅ CallDieuKhoan exists:', typeof CallDieuKhoan);
  //         console.log('✅ timeExpired exists:', typeof timeExpired);
  //
  //         console.log('🎉 All tests completed! Check Flutter logs for results.');
  //       };
  //     ''';
  //
  //     await webViewController.evaluateJavascript(source: testJS);
  //     print('✅ JavaScript interface test completed');
  //   } catch (e) {
  //     print('❌ JavaScript interface test failed: $e');
  //   }
  // }

  /// Escape string for JavaScript (prevent injection)
  String _escapeString(String input) {
    return input
        .replaceAll('\\', '\\\\')
        .replaceAll('"', '\\"')
        .replaceAll("'", "\\'")
        .replaceAll('\n', '\\n')
        .replaceAll('\r', '\\r')
        .replaceAll('\t', '\\t');
  }

  /// Get current screen type - exactly like Android repo getCurrentType() method
  Future<void> _getCurrentType([Function(String)? handler]) async {
    try {
      final result = await webViewController.evaluateJavascript(source: JavaScriptMethods.screenType);
      final screenType = result?.toString().replaceAll('"', '') ?? ScreenTypes.payment;

      print('🔍 Android Style: Current screen type: $screenType');

      if (handler != null) {
        handler(screenType);
        return;
      }

      // Handle screen type exactly like Android repo
      switch (screenType) {
        case ScreenTypes.voucher:
        case ScreenTypes.coupon:
        case ScreenTypes.betaPoint:
          print('🔙 Android Style: Calling backToMain() for screen type: $screenType');
          await _loadJs('${JavaScriptMethods.backToMain}();');
          break;

        case ScreenTypes.payment:
          print('🔙 Android Style: At payment screen, showing cancel dialog');
          _showCancelPaymentDialog();
          break;

        default:
          print('🔙 Android Style: Other screen type, checking if can go back');
          final canGoBack = await webViewController.canGoBack();
          if (canGoBack) {
            print('🔙 Android Style: Going back in WebView');
            await webViewController.goBack();
          } else {
            print('🔙 Android Style: Cannot go back, showing cancel dialog');
            _showCancelPaymentDialog();
          }
          break;
      }
    } catch (e) {
      print('❌ Android Style: Error getting screen type: $e');
      _showCancelPaymentDialog();
    }
  }

  /// Handle back press - simplified logic to always show cancel dialog
  Future<bool> _handleBackPress() async {
    try {
      print('🔙 Android Style: Back button pressed, showing cancel dialog');

      // Always show cancel confirmation dialog when back is pressed
      // This ensures user goes back to seat selection when they confirm
      bool shouldPop = false;
      await _showCancelPaymentConfirmDialog((result) {
        shouldPop = result;
      });

      return shouldPop; // Return true to pop back to seat selection, false to stay
    } catch (e) {
      print('❌ Android Style: Error in _handleBackPress: $e');

      // Default: show cancel dialog
      bool shouldPop = false;
      await _showCancelPaymentConfirmDialog((result) {
        shouldPop = result;
      });

      return shouldPop;
    }
  }

  /// Update current URL for navigation tracking
  void _updateCurrentUrl(String url) {
    print('🔗 Android Style: URL changed to: $url');

    // Set initial URL if not set
    if (_initialUrl == null) {
      _initialUrl = url;
      print('🏠 Android Style: Initial URL set: $_initialUrl');
    }

    _currentUrl = url;
    print('📍 Android Style: Current URL: $_currentUrl');
  }

  /// Handle back button press - exactly like Android repo getCurrentType() method
  Future<void> _handleBackButton() async {
    print('🔙 Android Style: Back button pressed');
    await _getCurrentType();
  }

  /// Handle system back button - exactly like webview_payment.dart _handleBackPress()
  Future<bool> _onWillPop() async {
    print('🔙 Android Style: System back button pressed');
    return await _handleBackPress();
  }

  /// Show cancel payment confirmation dialog - exactly like webview_payment.dart
  Future<void> _showCancelPaymentConfirmDialog(Function(bool) onResult) async {
    if (!mounted) return;

    bool shouldPop = false;
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text(
          'Hủy thanh toán',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        content: const Text(
          'Bạn có chắc chắn muốn hủy thanh toán?\n\nViệc hủy thanh toán sẽ làm mất ghế đã chọn.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              shouldPop = false;
            },
            child: const Text(
              'Không',
              style: TextStyle(
                color: Colors.grey,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              shouldPop = true;
              print('❌ Android Style: Payment cancelled by user');
            },
            child: const Text(
              'Có',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );

    onResult(shouldPop);
    if (shouldPop) {
      Navigator.of(context).pop(); // Exit payment screen
    }
  }

  /// Show cancel payment confirmation dialog (simple version)
  void _showCancelPaymentDialog() {
    _showCancelPaymentConfirmDialog((shouldPop) {
      if (shouldPop) {
        // Navigator.of(context).pop(); // Exit payment screen
      }
    });
  }

  /// Show payment success notification before navigating
  void _showPaymentSuccessNotification(String? transactionId) {
    if (!mounted) return;

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Row(
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 28,
            ),
            SizedBox(width: 12),
            Text(
              'Thanh toán thành công',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ],
        ),
        content: const Text(
          'Cảm ơn bạn đã đặt vé!\n\nVé của bạn đã được gửi đến email đăng ký.',
          style: TextStyle(fontSize: 16),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog

              // Navigate after showing notification
              Future.delayed(const Duration(milliseconds: 300), () {
                if (mounted) {
                  widget.onPaymentSuccess(transactionId);
                }
              });
            },
            child: const Text(
              'OK',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Handle URL navigation exactly like Android repo shouldOverrideUrlLoading
  Future<NavigationActionPolicy> _handleUrlNavigation(String url) async {
    print('🔗 Android Style: Processing URL navigation: $url');

    // Handle AirPay exactly like Android repo
    if (url.contains('airpay.vn') && await canLaunchUrl(Uri.parse(url))) {
      print('💳 Android Style: AirPay payment detected');
      widget.onPaymentMethodSelected('airpay');
      _trackPayment('confirm', 'airpay');
      await _launchExternalUrl(url);
      return NavigationActionPolicy.CANCEL;
    }

    // Handle MoMo with direct app launch option
    if (url.contains('momo.vn')) {
      print('💳 Android Style: MoMo payment detected');
      widget.onPaymentMethodSelected('momo');
      _trackPayment('confirm', 'momo');
      await _launchExternalUrl(url);
      return NavigationActionPolicy.CANCEL;
      // Try direct Momo app launch first
      final directMomoUrl = _generateDirectMomoUrl(url);
      if (await canLaunchUrl(Uri.parse(directMomoUrl))) {
        print('🚀 Launching Momo app directly: $directMomoUrl');
        await launchUrl(
          Uri.parse(directMomoUrl),
          mode: LaunchMode.externalApplication,
        );
        return NavigationActionPolicy.CANCEL;
      } else {
        // Fallback to web-based payment if Momo app not available
        print('⚠️ Momo app not available, falling back to web payment');
        if (await canLaunchUrl(Uri.parse(url))) {
          await _launchExternalUrl(url);
          return NavigationActionPolicy.CANCEL;
        }
      }
    }

    // Handle ZaloPay with direct app launch option
    if (url.contains('zalopay.vn')) {
      print('💳 Android Style: ZaloPay payment detected');
      widget.onPaymentMethodSelected('zalopay');
      _trackPayment('confirm', 'zalopay');

      // Try direct ZaloPay app launch first
      final directZaloPayUrl = _generateDirectZaloPayUrl(url);
      if (await canLaunchUrl(Uri.parse(directZaloPayUrl))) {
        print('🚀 Launching ZaloPay app directly: $directZaloPayUrl');
        await launchUrl(
          Uri.parse(directZaloPayUrl),
          mode: LaunchMode.externalApplication,
        );
        return NavigationActionPolicy.CANCEL;
      } else {
        // Fallback to web-based payment if ZaloPay app not available
        print('⚠️ ZaloPay app not available, falling back to web payment');
        if (await canLaunchUrl(Uri.parse(url))) {
          await _launchExternalUrl(url);
          return NavigationActionPolicy.CANCEL;
        }
      }
    }

    // Handle ShopeePay exactly like Android repo
    if (url.contains('shopee.vn') || url.contains('shopeepay')) {
      print('💳 Android Style: ShopeePay payment detected');
      widget.onPaymentMethodSelected('shopeepay');
      _trackPayment('confirm', 'shopeepay');

      // Try direct ShopeePay app launch first
      final directShopeePayUrl = _generateDirectShopeePayUrl(url);
      if (await canLaunchUrl(Uri.parse(directShopeePayUrl))) {
        print('🚀 Launching ShopeePay app directly: $directShopeePayUrl');
        await launchUrl(
          Uri.parse(directShopeePayUrl),
          mode: LaunchMode.externalApplication,
        );
        return NavigationActionPolicy.CANCEL;
      } else {
        // Fallback to web-based payment if ShopeePay app not available
        print('⚠️ ShopeePay app not available, falling back to web payment');
        if (await canLaunchUrl(Uri.parse(url))) {
          await _launchExternalUrl(url);
          return NavigationActionPolicy.CANCEL;
        }
      }
    }

    // Handle OnePay domestic exactly like Android repo
    if (url.contains('mtf.onepay.vn/paygate/vpcpay.op')) {
      print('💳 Android Style: OnePay domestic payment detected');
      widget.onPaymentMethodSelected('noidia');
      _trackPayment('confirm', 'noidia');
      return NavigationActionPolicy.ALLOW;
    }

    // Handle OnePay international exactly like Android repo
    if (url.contains('mtf.onepay.vn/promotion/vpcpost.op')) {
      print('💳 Android Style: OnePay international payment detected');
      widget.onPaymentMethodSelected('quocte');
      _trackPayment('confirm', 'quocte');
      return NavigationActionPolicy.ALLOW;
    }

    // Handle OnePay paygate
    if (url.contains('onepay.vn/paygate') || url.contains('mtf.onepay.vn/paygate')) {
      print('🏦 Android Style: OnePay paygate detected');
      return NavigationActionPolicy.ALLOW;
    }

    // Allow other URLs
    return NavigationActionPolicy.ALLOW;
  }

  /// Launch external payment app exactly like Android repo openLink()
  Future<void> _launchExternalUrl(String url) async {
    try {
      print('🔗 Android Style: Launching external payment app: $url');
      await launchUrl(
        Uri.parse(url),
        mode: LaunchMode.externalApplication,
      );
      print('✅ Android Style: External app launched successfully');
    } catch (e) {
      print('❌ Android Style: Error launching external app: $e');
      // Show error to user
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Không thể mở ứng dụng thanh toán: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Track payment method selection exactly like Android repo presenter.trackingConfirm()
  void _trackPayment(String type, String method) {
    print('📊 Android Style: Tracking payment - Type: $type, Method: $method');

    // TODO: Implement actual tracking logic here
    // This should call your analytics service
    // Example: AnalyticsService.track('payment_method_selected', {'method': method, 'type': type});

    // For now, just log the tracking event
    print('📈 Android Style: Payment tracking event - $type:$method');
  }

  /// Format time display exactly like iOS Style
  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  /// Show error dialog
  void _showError(String message) {
    if (!mounted) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Error'),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Go back to previous screen
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  /// Generate direct Momo app URL from web payment URL
  String _generateDirectMomoUrl(String webUrl) {
    try {
      final uri = Uri.parse(webUrl);

      // Extract payment parameters from web URL
      final orderId = uri.queryParameters['orderId'] ??
                     uri.queryParameters['order_id'] ??
                     'BETA_${DateTime.now().millisecondsSinceEpoch}';
      final amount = uri.queryParameters['amount'] ?? '0';
      final description = uri.queryParameters['description'] ??
                         uri.queryParameters['desc'] ??
                         'Beta Cinema Payment';

      // Generate direct Momo deep link
      final params = {
        'action': 'payment',
        'orderId': orderId,
        'amount': amount,
        'description': description,
        'callbackScheme': 'betacineplexx', // Your app's URL scheme
        'callbackHost': 'momo',
        'partnerCode': 'BETACINEMA',
        'requestId': 'REQ_${DateTime.now().millisecondsSinceEpoch}',
      };

      final queryString = params.entries
          .where((e) => e.value.isNotEmpty)
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final directUrl = 'momo://payment?$queryString';

      print('🔗 Generated direct Momo URL: $directUrl');
      return directUrl;

    } catch (e) {
      print('❌ Error generating direct Momo URL: $e');
      // Fallback to simple Momo app launch
      return 'momo://app';
    }
  }

  /// Generate direct ZaloPay app URL from web payment URL
  String _generateDirectZaloPayUrl(String webUrl) {
    try {
      final uri = Uri.parse(webUrl);

      // Extract payment parameters from web URL
      final appTransId = uri.queryParameters['appTransId'] ??
                        uri.queryParameters['apptransid'] ??
                        'BETA_${DateTime.now().millisecondsSinceEpoch}';
      final amount = uri.queryParameters['amount'] ?? '0';
      final description = uri.queryParameters['description'] ??
                         uri.queryParameters['desc'] ??
                         'Beta Cinema Payment';

      // Generate direct ZaloPay deep link
      final params = {
        'action': 'payment',
        'appTransId': appTransId,
        'amount': amount,
        'description': description,
        'callbackScheme': 'betacineplexx', // Your app's URL scheme
        'callbackHost': 'zalopay',
        'appId': '2553', // ZaloPay app ID (example)
      };

      final queryString = params.entries
          .where((e) => e.value.isNotEmpty)
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final directUrl = 'zalopay://payment?$queryString';

      print('🔗 Generated direct ZaloPay URL: $directUrl');
      return directUrl;

    } catch (e) {
      print('❌ Error generating direct ZaloPay URL: $e');
      // Fallback to simple ZaloPay app launch
      return 'zalopay://app';
    }
  }

  /// Generate direct ShopeePay app URL from web payment URL
  String _generateDirectShopeePayUrl(String webUrl) {
    try {
      final uri = Uri.parse(webUrl);

      // Extract payment parameters from web URL
      final orderId = uri.queryParameters['orderId'] ??
                     uri.queryParameters['order_id'] ??
                     'BETA_${DateTime.now().millisecondsSinceEpoch}';
      final amount = uri.queryParameters['amount'] ?? '0';
      final description = uri.queryParameters['description'] ??
                         uri.queryParameters['desc'] ??
                         'Beta Cinema Payment';

      // Generate direct ShopeePay deep link
      final params = {
        'action': 'payment',
        'orderId': orderId,
        'amount': amount,
        'description': description,
        'callbackScheme': 'betacineplexx', // Your app's URL scheme
        'callbackHost': 'shopeepay',
      };

      final queryString = params.entries
          .where((e) => e.value.isNotEmpty)
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value)}')
          .join('&');

      final directUrl = 'shopeepay://payment?$queryString';

      print('🔗 Generated direct ShopeePay URL: $directUrl');
      return directUrl;

    } catch (e) {
      print('❌ Error generating direct ShopeePay URL: $e');
      // Fallback to simple ShopeePay app launch
      return 'shopeepay://app';
    }
  }

  /// Handle OnePay payment result URL exactly like iOS Style and webview_payment.dart
  void _handlePaymentResult(String url) {
    print('✅ Android Style: Processing OnePay payment result from URL: $url');

    // Extract payment result from URL parameters
    final uri = Uri.parse(url);
    final params = uri.queryParameters;

    // Check for common payment result parameters
    if (params.containsKey('vpc_TxnResponseCode') ||
        params.containsKey('responseCode') ||
        params.containsKey('resultCode')) {

      print('📊 Android Style: Payment result parameters found: $params');

      // Process payment result
      _processPaymentResult(params);
    } else {
      print('⚠️ Android Style: No payment result parameters found in URL');
    }
  }

  /// Process OnePay payment result parameters exactly like iOS Style
  void _processPaymentResult(Map<String, String> params) {
    // Common success codes for different payment gateways
    final successCodes = ['0', '00', '000'];

    String? responseCode = params['vpc_TxnResponseCode'] ??
                          params['responseCode'] ??
                          params['resultCode'];

    // Extract transaction ID from URL parameters
    String? transactionId = params['vpc_TransactionNo'] ??
                           params['transactionId'] ??
                           params['tranId'];

    print('💳 Android Style: OnePay response code: $responseCode');
    print('💳 Android Style: OnePay transaction ID: $transactionId');

    // First, call JavaScript function to handle payment result in web context
    _loadJs('if (typeof processOnePayReturn === "function") { processOnePayReturn("${transactionId ?? ''}", "$responseCode"); }');

    if (responseCode != null && successCodes.contains(responseCode)) {
      print('✅ Android Style: OnePay payment successful with code: $responseCode');

      // Stop any countdown timer
      _preventFurtherNavigation();

      // Track payment success
      _trackPayment('success', 'onepay');

      // Call success callback with transaction ID exactly like Android repo postPaymentSuccess
      _handlePaymentSuccess(transactionId ?? '');

    } else {
      print('❌ Android Style: OnePay payment failed with code: $responseCode');

      // Track payment failure
      _trackPayment('fail', responseCode ?? "");

      // Call failure callback
      widget.onPaymentFailed('Thanh toán OnePay thất bại. Mã lỗi: $responseCode');
    }
  }
}
