import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/pages/other_tab/setting/faq_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/policy_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/version_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/widget/check_box_widget.dart';
import 'package:flutter_app/service/language_service.dart';
import 'package:flutter_app/service/geolocator_service.dart';
import 'package:flutter_app/service/native_location_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/widgets/language_switcher.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:geolocator/geolocator.dart';

import '../../../constants/index.dart';
import '../../../service/notification_service.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  @override
  State<SettingScreen> createState() => SettingScreenState();
}

class SettingScreenState extends State<SettingScreen> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = false; // ✅ Add location setting with geolocator
  String _appVersion = '1.0.0';

  final GeolocatorService _geolocatorService = GeolocatorService();

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // Get app version like iOS/Android
    final packageInfo = await PackageInfo.fromPlatform();

    // Check location permission and service status using geolocator
    final locationEnabled = await _checkLocationStatus();

    setState(() {
      _notificationsEnabled = prefs.getBool('notificationsEnabled') ?? true;
      _locationEnabled = locationEnabled; // ✅ Load location setting using geolocator
      _appVersion = packageInfo.version; // ✅ Get real version like iOS/Android
    });
  }

  /// Check location status using native location service
  Future<bool> _checkLocationStatus() async {
    try {
      bool serviceEnabled = await NativeLocationService.isLocationServiceEnabled();
      if (!serviceEnabled) return false;

      LocationPermission permission = await NativeLocationService.checkLocationPermission();
      return permission == LocationPermission.whileInUse || permission == LocationPermission.always;
    } catch (e) {
      print('❌ Error checking location status: $e');
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(title: 'Setting.Title'.tr(), titleColor: Colors.white),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildLanguageSection(),
          const SizedBox(height: 16),
          _buildOtherSection(),
        ],
      ),
    );
  }

  Widget _buildLanguageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            'Setting.Language'.tr(),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        // Option 1: Use the original checkbox style
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Column(
            children: [
              CheckboxCell(
                title: 'Setting.VietNam'.tr(),
                isChecked: context.locale == const Locale('vi'),
                isTop: true,
                onTap: () => _changeLanguage(false, context),
              ),
              const Divider(height: 1),
              CheckboxCell(
                title: 'Setting.English'.tr(),
                isChecked: context.locale == const Locale('en'),
                isBottom: true,
                onTap: () => _changeLanguage(true, context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOtherSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            'pages.login.register.Other'.tr(),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Column(
            children: [
              SwitchCell(
                title: 'Setting.Notify'.tr(),
                value: _notificationsEnabled,
                onChanged: (value) => _toggleNotifications(value),
              ),
              const Divider(height: 1),
              SwitchCell(
                title: 'Setting.Location'.tr(), // ✅ Location setting with geolocator
                value: _locationEnabled,
                onChanged: (value) async {
                  print('🔄 Location switch onChanged called with: $value');

                  if (value) {
                    // User wants to enable location
                    await _enableLocation();
                  } else {
                    // User wants to disable location
                    await _disableLocation();
                  }
                },
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.FAQ'.tr(),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const FAQScreen()),
                ),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.Version'.tr(),
                onTap: () => Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const VersionInfoScreen(),
                    )),
                content: _appVersion,
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.Policy'.tr(),
                onTap: () => _showPolicy('privacy'),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.PaymentPolicy'.tr(),
                onTap: () => _showPolicy('payment'),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.SecurePolicy'.tr(),
                onTap: () => _showPolicy('security'),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.CompanyInfo'.tr(),
                onTap: () => _showCompanyInfo(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _changeLanguage(bool isEnglish, BuildContext context) async {
    final languageService = LanguageService();
    final languageCode = isEnglish ? 'en' : 'vi';

    // Save language preference and update app locale
    await languageService.setLanguage(context, languageCode);

    // Also save to the old preference key for backward compatibility
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isEnglish', isEnglish);

    // Show a dialog to confirm the language change
    if (mounted) {
      _showLanguageChangedDialog(isEnglish, context);
    }
  }

  void _showLanguageChangedDialog(bool isEnglish, BuildContext context1) {
    UDialog()
        .showSuccess(
            text: isEnglish ? 'Language has been changed to English' : 'Ngôn ngữ đã được chuyển sang Tiếng Việt',
            title: 'Setting.Language'.tr())
        .then(
          (value) => context1.goNamed(CRoute.splash),
        );
  }

  Future<void> _toggleNotifications(bool value) async {
    // Request notification permissions if enabling notifications
    if (value) {
      // This would typically use a plugin like firebase_messaging or flutter_local_notifications
      // to request notification permissions
      // For now, we'll just show a dialog to inform the user
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Setting.Notify'.tr()),
            content: Text(
              context.locale.languageCode == 'en'
                  ? 'You will now receive notifications from the app.'
                  : 'Bạn sẽ nhận được thông báo từ ứng dụng.',
            ),
            actions: [
              TextButton(
                onPressed: () async {
                  final prefs = await SharedPreferences.getInstance();
                  await prefs.setBool('notificationsEnabled', value);
                  setState(() {
                    _notificationsEnabled = value;
                  });
                  Navigator.of(context).pop();
                },
                child: Text('Bt.OK'.tr()),
              ),
            ],
          ),
        );
      }
    } else {
      // Show dialog to inform user notifications are disabled
      if (mounted) {
        UDialog().showConfirm(
          title: 'Setting.Notify'.tr(),
          text: context.locale.languageCode == 'en'
              ? 'Are you sure you want to disable notifications ?'
              : 'Bạn có chắc chắn muốn tắt thông báo ?',
          btnOkOnPress: () async {
            // Unregister FCM token if user is logged in
            if (context.read<AuthC>().state.user != null) {
              print('🔄 Unregistering FCM token...');
              final notifi = NotificationService();
              await notifi.unregisterFCMToken();
            }

            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('notificationsEnabled', value);
            setState(() {
              _notificationsEnabled = value;
            });
            Navigator.of(context).pop();
          },
        );
      }
    }
  }

  /// Enable location services with native dialog
  Future<void> _enableLocation() async {
    try {
      print('🔄 Enabling location with native dialog...');

      // Use the native location service for complete setup
      final result = await NativeLocationService.setupLocation();

      if (result.success) {
        // Success - location is enabled
        setState(() {
          _locationEnabled = true;
        });

        // Save to preferences
        final prefs = await SharedPreferences.getInstance();
        await prefs.setBool('locationEnabled', true);

        print('✅ Location enabled successfully');

        UDialog().showSuccess(
          title: 'Định vị',
          text: result.message,
        );
      } else {
        // Failed - handle different actions
        setState(() {
          _locationEnabled = false;
        });

        switch (result.action) {
          case LocationSetupAction.openLocationSettings:
            await _showLocationServiceDialog();
            break;
          case LocationSetupAction.openAppSettings:
            await _showLocationPermissionPermanentlyDeniedDialog();
            break;
          case LocationSetupAction.requestPermission:
            UDialog().showError(
              title: 'Định vị',
              text: result.message,
            );
            break;
          case LocationSetupAction.none:
            UDialog().showError(
              title: 'Lỗi',
              text: result.message,
            );
            break;
        }
      }
    } catch (e) {
      print('❌ Error enabling location: $e');
      setState(() {
        _locationEnabled = false;
      });

      UDialog().showError(
        title: 'Profile.Error'.tr(),
        text: 'Không thể bật định vị: ${e.toString()}',
      );
    }
  }

  /// Request location service with native dialog
  Future<bool> _requestLocationService() async {
    try {
      print('🔄 Requesting location service...');

      // Check current status
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

      if (!serviceEnabled) {
        print('⚠️ Location services disabled, trying to show native dialog...');

        // Method 1: Try to trigger native dialog by requesting permission
        // This often shows the system dialog on Android
        try {
          LocationPermission permission = await Geolocator.requestPermission();
          print('📍 Permission request result: $permission');

          // Check if location service is now enabled
          serviceEnabled = await Geolocator.isLocationServiceEnabled();

          if (serviceEnabled) {
            print('✅ Location service enabled via permission request');
            return true;
          }
        } catch (e) {
          print('⚠️ Permission request failed: $e');
        }

        // Method 2: Try platform-specific approach
        if (!serviceEnabled) {
          serviceEnabled = await _showNativeLocationDialog();
        }

        // Method 3: Fallback to settings dialog
        if (!serviceEnabled) {
          await _showLocationServiceDialog();

          // Give user time to enable and check again
          await Future.delayed(const Duration(seconds: 1));
          serviceEnabled = await Geolocator.isLocationServiceEnabled();
        }
      }

      print('📍 Final location service status: $serviceEnabled');
      return serviceEnabled;
    } catch (e) {
      print('❌ Error requesting location service: $e');
      return false;
    }
  }

  /// Try to show native location dialog using platform-specific methods
  Future<bool> _showNativeLocationDialog() async {
    try {
      // For Android: Try to use a more direct approach
      // This attempts to show the native "Turn on location" dialog

      // Check if we're on Android
      if (Theme.of(context).platform == TargetPlatform.android) {
        print('📱 Attempting to show Android native location dialog...');

        // Try to request location with high accuracy
        // This sometimes triggers the native dialog
        try {
          await Geolocator.getCurrentPosition(
            desiredAccuracy: LocationAccuracy.high,
            timeLimit: const Duration(seconds: 1),
          );

          // If we get here, location is working
          return true;
        } catch (e) {
          print('⚠️ getCurrentPosition failed (expected): $e');

          // The failure might have triggered the native dialog
          // Wait a moment and check again
          await Future.delayed(const Duration(milliseconds: 500));
          bool serviceEnabled = await Geolocator.isLocationServiceEnabled();

          if (serviceEnabled) {
            print('✅ Location enabled after getCurrentPosition attempt');
            return true;
          }
        }
      }

      return false;
    } catch (e) {
      print('❌ Error showing native location dialog: $e');
      return false;
    }
  }

  /// Show dialog to guide user to enable location services
  Future<void> _showLocationServiceDialog() async {
    await UDialog().showConfirm(
      title: 'Bật dịch vụ định vị',
      text: 'Ứng dụng cần sử dụng dịch vụ định vị. Vui lòng bật dịch vụ định vị trong cài đặt thiết bị.',
      btnOkText: 'Mở cài đặt',
      btnCancelText: 'Hủy',
      btnOkOnPress: () async {
        // Close dialog first
        if (mounted) Navigator.of(context).pop();

        // Open location settings using native service
        await NativeLocationService.openLocationSettings();

        // Check if location is now enabled after user returns
        Future.delayed(const Duration(seconds: 1), () async {
          final isEnabled = await NativeLocationService.isLocationServiceEnabled();
          setState(() {
            _locationEnabled = isEnabled;
          });

          if (isEnabled) {
            // Save to preferences
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('locationEnabled', true);

            UDialog().showSuccess(
              title: 'Định vị',
              text: 'Định vị đã được bật thành công!',
            );
          }
        });
      },
      btnCancelOnPress: () {
        if (mounted) Navigator.of(context).pop();
      },
    );
  }

  /// Disable location services
  Future<void> _disableLocation() async {
    try {
      print('🔄 Disabling location...');

      setState(() {
        _locationEnabled = false;
      });

      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('locationEnabled', false);

      print('✅ Location disabled successfully');

      UDialog().showSuccess(
        title: 'Định vị',
        text: 'Định vị đã được tắt. Bạn có thể bật lại bất cứ lúc nào.',
      );
    } catch (e) {
      print('❌ Error disabling location: $e');
      UDialog().showError(
        title: 'Lỗi',
        text: 'Không thể tắt định vị: ${e.toString()}',
      );
    }
  }

  /// Show dialog when location services are disabled
  Future<void> _showLocationServiceDisabledDialog() async {
    await UDialog().showConfirm(
      title: 'Dịch vụ định vị bị tắt',
      text: 'Dịch vụ định vị hiện đang bị tắt. Bạn có muốn mở cài đặt để bật dịch vụ định vị không?',
      btnOkText: 'Mở cài đặt',
      btnCancelText: 'Hủy',
      btnOkOnPress: () async {
        // Close dialog first
        if (mounted) Navigator.of(context).pop();

        // Open location settings
        await _geolocatorService.openLocationSettings();

        // Recheck location status after user returns
        Future.delayed(const Duration(seconds: 1), () async {
          final isEnabled = await _checkLocationStatus();
          setState(() {
            _locationEnabled = isEnabled;
          });
        });
      },
      btnCancelOnPress: () {
        if (mounted) Navigator.of(context).pop();
        setState(() {
          _locationEnabled = false;
        });
      },
    );
  }

  /// Show dialog when location permission is permanently denied
  Future<void> _showLocationPermissionPermanentlyDeniedDialog() async {
    await UDialog().showConfirm(
      title: 'Quyền định vị bị từ chối',
      text: 'Quyền truy cập vị trí đã bị từ chối vĩnh viễn. Bạn có muốn mở cài đặt ứng dụng để cấp quyền không?',
      btnOkText: 'Mở cài đặt',
      btnCancelText: 'Hủy',
      btnOkOnPress: () async {
        // Close dialog first
        if (mounted) Navigator.of(context).pop();

        // Open app settings using native service
        await NativeLocationService.openAppSettings();

        // Recheck permission status after user returns
        Future.delayed(const Duration(seconds: 1), () async {
          final isEnabled = await NativeLocationService.isLocationServiceEnabled();
          final permission = await NativeLocationService.checkLocationPermission();

          final locationEnabled =
              isEnabled && (permission == LocationPermission.whileInUse || permission == LocationPermission.always);

          setState(() {
            _locationEnabled = locationEnabled;
          });

          if (locationEnabled) {
            // Save to preferences
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('locationEnabled', true);

            UDialog().showSuccess(
              title: 'Định vị',
              text: 'Định vị đã được bật thành công!',
            );
          }
        });
      },
      btnCancelOnPress: () {
        if (mounted) Navigator.of(context).pop();
        setState(() {
          _locationEnabled = false;
        });
      },
    );
  }

  /// Test current location using geolocator
  Future<void> _testCurrentLocation() async {
    try {
      // Check if location is enabled
      if (!_locationEnabled) {
        UDialog().showSuccess(
          title: 'Định vị',
          text: 'Vui lòng bật định vị trước khi kiểm tra vị trí hiện tại.',
        );
        return;
      }

      // Get current position using native service
      Position? position = await NativeLocationService.getCurrentPosition(
        accuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 5),
      );

      if (position == null) {
        throw Exception('Không thể lấy vị trí hiện tại');
      }

      // Hide loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show success dialog with coordinates
      UDialog().showSuccess(
        title: 'Vị trí hiện tại',
        text: 'Tọa độ: ${position.latitude}, ${position.longitude}\n'
            'Độ chính xác: ${position.accuracy.toStringAsFixed(2)} mét',
      );
    } catch (e) {
      // Hide loading dialog if showing
      if (mounted) Navigator.of(context).pop();

      // Show error dialog
      UDialog().showError(
        title: 'Lỗi định vị',
        text: 'Không thể lấy vị trí hiện tại: ${e.toString()}',
      );
      print('❌ Error getting current location: $e');
    }
  }

  void _showPolicy(String type) {
    PolicyType policyType;

    switch (type) {
      case 'privacy':
        policyType = PolicyType.terms;
        break;
      case 'payment':
        policyType = PolicyType.payment;
        break;
      case 'security':
        policyType = PolicyType.security;
        break;
      default:
        policyType = PolicyType.terms;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PolicyScreen(policyType: policyType),
      ),
    );
  }

  void _showCompanyInfo() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PolicyScreen(policyType: PolicyType.companyInfo),
      ),
    );
  }
}
