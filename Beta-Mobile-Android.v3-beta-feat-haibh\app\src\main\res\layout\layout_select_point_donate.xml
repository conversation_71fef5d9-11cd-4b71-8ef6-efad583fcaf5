<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="20dp" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFirstContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="@string/donate_point_title"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDonateUser"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:textColor="@color/black"
        android:textSize="18sp"
        app:fontFamily="@font/sanspro_bold"
        tools:text="9782185950" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDonateEmail"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"
        tools:text="(<EMAIL>)" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNoteDonateInput"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="@string/input_your_point_donate"
        android:textColor="@color/black"
        android:textSize="18sp"
        app:fontFamily="@font/sanspro_bold" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv5Points"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="15dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:tag="5"
            android:background="@drawable/border_gray_radius"
            android:gravity="center"
            android:paddingLeft="2dp"
            android:paddingTop="5dp"
            android:paddingRight="2dp"
            android:paddingBottom="5dp"
            android:text="5"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_regular" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv10Points"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:background="@drawable/border_gray_radius"
            android:gravity="center"
            android:paddingLeft="2dp"
            android:paddingTop="5dp"
            android:paddingRight="2dp"
            android:tag="10"
            android:paddingBottom="5dp"
            android:text="10"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_regular" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv20Points"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:background="@drawable/shape_primary_radius"
            android:gravity="center"
            android:tag="20"
            android:paddingLeft="2dp"
            android:paddingTop="5dp"
            android:paddingRight="2dp"
            android:paddingBottom="5dp"
            android:text="20"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_regular" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv30Points"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="5dp"
            android:layout_weight="1"
            android:background="@drawable/border_gray_radius"
            android:gravity="center"
            android:tag="30"
            android:paddingLeft="2dp"
            android:paddingTop="5dp"
            android:paddingRight="2dp"
            android:paddingBottom="5dp"
            android:text="30"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_regular" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv100Points"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="5dp"
            android:layout_marginRight="15dp"
            android:layout_weight="1"
            android:background="@drawable/border_gray_radius"
            android:gravity="center"
            android:tag="100"
            android:paddingLeft="2dp"
            android:paddingTop="5dp"
            android:paddingRight="2dp"
            android:paddingBottom="5dp"
            android:text="100"
            android:textColor="@color/black"
            android:textSize="16sp"
            app:fontFamily="@font/oswald_regular" />
    </LinearLayout>


    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/edtInputPoint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginTop="10dp"
        android:layout_marginRight="15dp"
        android:background="@drawable/border_gray_radius"
        android:fontFamily="@font/oswald_regular"
        android:hint="@string/enter_another_number"
        android:inputType="textCapCharacters|numberSigned"
        android:paddingLeft="10dp"
        android:paddingTop="10dp"
        android:paddingRight="10dp"
        android:paddingBottom="10dp"
        android:textAllCaps="true"
        android:textSize="14sp" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="30dp"
        android:background="@color/grayLine" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnDonateCancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:fontFamily="@font/sanspro_bold"
            android:text="@string/cancel"
            android:textColor="@color/colorPrimaryDark" />

        <View
            android:layout_width="0.5dp"
            android:layout_height="match_parent"
            android:background="@color/grayLine" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btnDonateOk"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:background="?attr/selectableItemBackground"
            android:fontFamily="@font/sanspro_bold"
            android:text="@string/agree"
            android:textColor="@color/colorPrimaryDark" />
    </LinearLayout>


    <View
        android:layout_width="match_parent"
        android:layout_height="5dp" />
</LinearLayout>