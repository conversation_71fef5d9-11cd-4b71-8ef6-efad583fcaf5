<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_margin="@dimen/margin_small"
    app:cardCornerRadius="3dp"
    app:cardElevation="0dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/contentView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            app:layout_constraintTop_toTopOf="parent"
            android:id="@+id/tvTitle"
            style="@style/TextContent"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            android:paddingTop="15dp"
            tools:text="Quà tặng từ Mr &amp; Mrs Grey-Special gift from Mr &amp; Mrs Grey"
            android:textColor="@color/black"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@+id/ivDropdown"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivDropdown"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:padding="12dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:srcCompat="@drawable/ic_dropdown"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvDate"
            style="@style/TextContent"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingBottom="8dp"
            android:paddingLeft="15dp"
            android:paddingTop="5dp"
            tools:text="10/04/2018"
            app:layout_constraintLeft_toLeftOf="parent"
            android:textColor="@color/text3fb7f9"
            app:layout_constraintTop_toBottomOf="@+id/tvTitle"/>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?android:attr/selectableItemBackground"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <View
            android:id="@+id/viewUnread"
            android:visibility="gone"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_marginLeft="5dp"
            android:layout_marginTop="5dp"
            android:layout_width="10dp"
            android:layout_height="10dp"
            tools:visibility="visible"
            android:background="@drawable/shape_blue_rounded"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.cardview.widget.CardView>