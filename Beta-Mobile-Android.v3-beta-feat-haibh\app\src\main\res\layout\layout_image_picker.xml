<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical" android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp">
    <TextView
        android:id="@+id/btRetry"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_selector"
        android:textColor="@color/colorPrimary"
        android:text="@string/try_again"
        android:gravity="center"
        android:padding="10dp"
        android:textSize="@dimen/font_large"/>
    <View
        android:id="@+id/bottomLineRetry"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/grayBackground"/>
    <TextView
        android:id="@+id/btTakeImage"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_selector"
        android:textColor="@color/colorPrimary"
        android:text="@string/take_photo"
        android:gravity="center"
        android:padding="10dp"
        android:textSize="@dimen/font_large"/>
    <View
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:background="@color/grayBackground"/>
    <TextView
        android:id="@+id/btGallery"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_selector"
        android:textColor="@color/colorPrimary"
        android:text="@string/select_from_gallery"
        android:gravity="center"
        android:padding="10dp"
        android:textSize="@dimen/font_large"/>

    <TextView
        android:id="@+id/btCancel"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_white_selector"
        android:textColor="@color/colorPrimary"
        android:text="@string/cancel"
        android:textStyle="bold"
        android:layout_marginTop="10dp"
        android:gravity="center"
        android:padding="10dp"
        android:textSize="@dimen/font_large"/>
</LinearLayout>