package vn.zenity.betacineplex.view.user

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_member_card.*
import kotlinx.android.synthetic.main.item_member_card.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.getColor
import vn.zenity.betacineplex.helper.extension.gone
import vn.zenity.betacineplex.helper.extension.visible
import vn.zenity.betacineplex.model.CardModel

/**
 * Created by Zenity.
 */

class MemberCardFragment : BaseFragment(), MemberCardContractor.View {

    override fun showMemberCard(cards: List<CardModel>) {
        activity?.runOnUiThread {
            if (cards.isEmpty()) {
                this.listCard = listOf()
                recyclerView?.adapter?.notifyDataSetChanged()
            } else {
                this.listCard = cards
                recyclerView?.adapter?.notifyDataSetChanged()
            }
        }
    }

    private val presenter = MemberCardPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_member_card
    }

    private var listCard: List<CardModel> = listOf()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView?.layoutManager = androidx.recyclerview.widget.LinearLayoutManager(context)
        recyclerView?.adapter = Adapter()
        presenter.getMemberCard(Global.share().user?.AccountId ?: return)
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        private val TYPE_EVENT = 1
        private val TYPE_HEADER = 0

        override fun getItemCount(): Int {
            return 1 + listCard.size
        }

        override fun getItemViewType(position: Int): Int {
            if (position == 0) return TYPE_HEADER
            return TYPE_EVENT
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (getItemViewType(position) == TYPE_EVENT) {
                if(position != itemCount - 1) {
                    holder.itemView.bottomLine.gone()
                } else {
                    holder.itemView.bottomLine.visible()
                }
                holder.itemView.tvName.text = listCard[position - 1].ClassName
                holder.itemView.tvNumber.text = listCard[position - 1].CardNumber
                holder.itemView.tvDate.text = listCard[position - 1].CreatedOnDate?.dateConvertFormat(Constant.DateFormat.defaultFullSavis, Constant.DateFormat.dateSavis)
                if(listCard[position - 1].Status == 1) {
                    holder.itemView.tvName.setTextColor(R.color.colorPrimary.getColor())
                    holder.itemView.tvNumber.setTextColor(R.color.colorPrimary.getColor())
                    holder.itemView.tvDate.setTextColor(R.color.colorPrimary.getColor())
                    holder.itemView.tvUsing.visible()
                } else {
                    holder.itemView.tvUsing.gone()
                    holder.itemView.tvName.setTextColor(R.color.black.getColor())
                    holder.itemView.tvNumber.setTextColor(R.color.black.getColor())
                    holder.itemView.tvDate.setTextColor(R.color.black.getColor())
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            if (viewType == TYPE_HEADER) {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_member_card_header, parent, false)
                return Holder(itemView)
            } else {
                val itemView = LayoutInflater.from(parent.context).inflate(R.layout.item_member_card, parent, false)
                return Holder(itemView)
            }
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}
