<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/action_bar"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/shape_white_radius"
    android:layout_marginLeft="@dimen/activity_horizontal_margin"
    android:layout_marginRight="@dimen/activity_horizontal_margin"
    android:layout_marginBottom="@dimen/activity_horizontal_margin"
    android:paddingTop="16dp"
    android:paddingBottom="16dp"
    android:paddingLeft="16dp"
    android:paddingRight="16dp"
    android:orientation="horizontal"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    android:gravity="center_vertical">
    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/ivAvatar"
        android:layout_width="40dp"
        android:layout_height="40dp"
        android:src="@drawable/test_event"/>
    <LinearLayout
        android:layout_width="0dp"
        android:layout_weight="1"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/activity_horizontal_margin"
        android:orientation="vertical"
        android:gravity="center_vertical">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUsername"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:text="Hi, Đỗ Ngọc Vinh"
            android:textColor="@color/text1e1f28"
            android:textSize="18sp"
            app:fontFamily="@font/sanspro_bold"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvEmail"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="<EMAIL>"
            android:layout_marginTop="2dp"
            android:textSize="14sp"
            app:fontFamily="@font/sanspro_regular"/>
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/btnGivePoint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/donate"
        app:textAllCaps="true"
        android:textColor="@color/colorPrimaryDark"
        android:background="@drawable/border_colorapp_radius"
        android:textSize="16sp"
        android:fontFamily="@font/oswald_regular"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"/>
</LinearLayout>