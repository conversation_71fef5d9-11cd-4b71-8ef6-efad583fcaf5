import 'dart:math';
import 'package:flutter/material.dart';
import 'package:fl_location/fl_location.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:geolocator/geolocator.dart' as geo;

class LocationService {
  static final LocationService _instance = LocationService._internal();

  factory LocationService() {
    return _instance;
  }

  LocationService._internal();

  Location? _currentLocation;

  Location? get currentLocation => _currentLocation;

  /// Determine the current position of the device.
  /// When the location services are not enabled or permissions
  /// are denied the `Future` will return an error.
  Future<Location?> determinePosition({bool showError = true, BuildContext? context}) async {
    bool serviceEnabled;
    LocationPermission permission;

    // Test if location services are enabled.
    serviceEnabled = await FlLocation.isLocationServicesEnabled;
    if (!serviceEnabled) {
      // Location services are not enabled, show dialog to enable
      if (showError && context != null) {
        final shouldEnable = await _showLocationServicesDisabledDialog(context);
        if (shouldEnable) {
          // Try to enable location services using Geolocator
          try {
            // Check if location services are enabled using Geolocator
            bool geoServiceEnabled = await geo.Geolocator.isLocationServiceEnabled();
            if (!geoServiceEnabled) {
              // Try to open location settings
              bool opened = await geo.Geolocator.openLocationSettings();
              if (opened) {
                // Wait a bit for user to enable location services
                await Future.delayed(const Duration(seconds: 1));
                // Check again if services are now enabled using helper method
                serviceEnabled = await _isLocationServiceEnabled();
                if (!serviceEnabled) {
                  return Future.error('Location services are still disabled.');
                }
              } else {
                return Future.error('Could not open location settings.');
              }
            }
          } catch (e) {
            return Future.error('Failed to enable location services: $e');
          }
        } else {
          return Future.error('Location services are disabled.');
        }
      } else {
        return Future.error('Location services are disabled.');
      }
    }

    permission = await FlLocation.checkLocationPermission();
    if (permission == LocationPermission.denied) {
      // Show dialog asking for permission first
      if (showError && context != null) {
        final shouldRequest = await _showPermissionRequestDialog(context);
        if (shouldRequest) {
          permission = await FlLocation.requestLocationPermission();
          if (permission == LocationPermission.denied) {
            // Permissions are denied after request, show error
            _showPermissionDeniedDialog(context);
            return Future.error('Location permissions are denied');
          }
        } else {
          return Future.error('Location permissions are denied');
        }
      } else {
        // No context to show dialog, just request permission
        permission = await FlLocation.requestLocationPermission();
        if (permission == LocationPermission.denied) {
          return Future.error('Location permissions are denied');
        }
      }
    }

    if (permission == LocationPermission.deniedForever) {
      // Permissions are denied forever, handle appropriately.
      if (showError && context != null) {
        _showPermissionPermanentlyDeniedDialog(context);
      }
      return Future.error(
          'Location permissions are permanently denied, we cannot request permissions.');
    }

    // When we reach here, permissions are granted and we can
    // continue accessing the position of the device.
    try {
      _currentLocation = await FlLocation.getLocation(
        timeLimit: const Duration(seconds: 5),
        accuracy: LocationAccuracy.high,
      );
      return _currentLocation;
    } catch (e) {
      // If getting high accuracy position fails, try with lower accuracy
      try {
        _currentLocation = await FlLocation.getLocation(
          timeLimit: const Duration(seconds: 5),
          accuracy: LocationAccuracy.low,
        );
        return _currentLocation;
      } catch (e) {
        if (showError && context != null) {
          print(e.toString());
          // _showLocationErrorDialog(context, e.toString());
        }
        return null;
      }
    }
  }

  /// Calculate distance between two coordinates in kilometers
  double calculateDistance(double lat1, double lon1, double lat2, double lon2) {
    const double earthRadius = 6371; // Radius of the earth in km

    double dLat = _degreesToRadians(lat2 - lat1);
    double dLon = _degreesToRadians(lon2 - lon1);

    double a = sin(dLat / 2) * sin(dLat / 2) +
        cos(_degreesToRadians(lat1)) * cos(_degreesToRadians(lat2)) *
        sin(dLon / 2) * sin(dLon / 2);

    double c = 2 * atan2(sqrt(a), sqrt(1 - a));
    double distance = earthRadius * c; // Distance in km

    return distance;
  }

  double _degreesToRadians(double degrees) {
    return degrees * (pi / 180);
  }

  /// Format distance to a readable string
  String formatDistance(double distanceInKm) {
    if (distanceInKm < 1) {
      // Convert to meters if less than 1 km
      int meters = (distanceInKm * 1000).round();
      return '$meters m';
    } else if (distanceInKm < 10) {
      // Show one decimal place if less than 10 km
      return '${distanceInKm.toStringAsFixed(1)} km';
    } else {
      // Round to nearest km if 10 km or more
      return '${distanceInKm.round()} km';
    }
  }

  Future<bool> _showLocationServicesDisabledDialog(BuildContext context) async {
    bool shouldEnable = false;

    await UDialog().showConfirm(
      title: 'Location.ServiceDisabled'.tr(),
      text: 'Location.EnableLocationServiceMessage'.tr(),
      btnOkText: 'Location.EnableLocation'.tr(),
      btnCancelText: 'Common.Cancel'.tr(),
      btnOkOnPress: () async {
        shouldEnable = true;
        Navigator.of(context).pop();
      },
      btnCancelOnPress: () {
        shouldEnable = false;
        Navigator.of(context).pop();
      },
    );

    return shouldEnable;
  }

  Future<bool> _showPermissionRequestDialog(BuildContext context) async {
    bool shouldRequest = false;

    await UDialog().showConfirm(
      title: 'Location.PermissionTitle'.tr(),
      text: 'Location.PermissionMessage'.tr(),
      btnOkText: 'Location.GrantPermission'.tr(),
      btnCancelText: 'Common.Cancel'.tr(),
      btnOkOnPress: () async {
        shouldRequest = true;
        Navigator.of(context).pop();
      },
      btnCancelOnPress: () {
        shouldRequest = false;
        Navigator.of(context).pop();
      },
    );

    return shouldRequest;
  }

  void _showPermissionDeniedDialog(BuildContext context) {
    UDialog().showConfirm(
      title: 'Location.PermissionDenied'.tr(),
      text: 'Location.PermissionDeniedMessage'.tr(),
      btnOkText: 'Location.GrantPermission'.tr(),
      btnCancelText: 'Common.Cancel'.tr(),
      btnOkOnPress: () async {
        Navigator.of(context).pop();
        await FlLocation.requestLocationPermission();
      },
      btnCancelOnPress: () {
        Navigator.of(context).pop();
      },
    );
  }

  void _showPermissionPermanentlyDeniedDialog(BuildContext context) {
    UDialog().showConfirm(
      title: 'Location.PermissionPermanentlyDenied'.tr(),
      text: 'Location.PermissionPermanentlyDeniedMessage'.tr(),
      btnOkText: 'Location.OpenSettings'.tr(),
      btnCancelText: 'Common.Cancel'.tr(),
      btnOkOnPress: () async {
        Navigator.of(context).pop();
        openAppSettings();
      },
      btnCancelOnPress: () {
        Navigator.of(context).pop();
      },
    );
  }

  void _showLocationErrorDialog(BuildContext context, String error) {
    UDialog().showError(
      title: 'Location.LocationError'.tr(),
      text: 'Location.LocationErrorMessage'.tr(namedArgs: {'error': error}),
      onTap: () {
        Navigator.of(context).pop();
      },
    );
  }

  /// Check if location services are enabled using both libraries
  Future<bool> _isLocationServiceEnabled() async {
    try {
      final flLocationEnabled = await FlLocation.isLocationServicesEnabled;
      return flLocationEnabled;
    } catch (e) {
      print('❌ Error checking location service status: $e');
      return false;
    }
  }
}
