import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';

class FreeVoucherScreen extends StatefulWidget {
  const FreeVoucherScreen({super.key});

  @override
  State<FreeVoucherScreen> createState() => _FreeVoucherScreenState();
}

class _FreeVoucherScreenState extends State<FreeVoucherScreen> {
  bool _isLoading = true;
  List<PromotionItem> _vouchers = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchFreeVouchers();
  }

  Future<void> _fetchFreeVouchers() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get free vouchers
      final result = await RepositoryProvider.of<Api>(context).promotion.getFreeVouchers();

      if (result != null) {
        final List<dynamic> voucherData = result.data['content'] ?? [];
        setState(() {
          _vouchers = voucherData.map((item) => PromotionItem.fromJson(item)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load free vouchers';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'free_voucher'.tr(),
        titleColor: Colors.white
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchFreeVouchers,
              child:  Text('Bt.Retry'.tr()),
            ),
          ],
        ),
      );
    }

    if (_vouchers.isEmpty) {
      return  Center(child: Text('Voucher.NoFreeVouchers'.tr()));
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _vouchers.length,
      itemBuilder: (context, index) {
        final item = _vouchers[index];
        return VoucherCard(
          item: item,
          onTap: () => _navigateToDetail(item),
        );
      },
    );
  }

  void _navigateToDetail(PromotionItem item) {
    context.pushNamed(CRoute.newsDetail, extra: item);
  }
}

class VoucherCard extends StatelessWidget {
  final PromotionItem item;
  final VoidCallback onTap;

  const VoucherCard({
    super.key,
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Format the date if available
    String formattedDate = '';
    if (item.publishDate != null) {
      try {
        final date = DateTime.parse(item.publishDate!);
        formattedDate = DateFormat('dd/MM/yyyy').format(date);
      } catch (e) {
        formattedDate = item.publishDate!;
      }
    }

    return Card(
      elevation: 4,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image
            if (item.imageUrl != null)
              ClipRRect(
                borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
                child: Image.network(
                  item.imageUrl!,
                  width: double.infinity,
                  height: 150,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) {
                    return Container(
                      width: double.infinity,
                      height: 150,
                      color: Colors.grey[300],
                      child: const Icon(Icons.image_not_supported, size: 50),
                    );
                  },
                ),
              ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF223849),
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Date
                  if (formattedDate.isNotEmpty)
                    Text(
                      'Có hiệu lực đến: $formattedDate',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  const SizedBox(height: 8),

                  // Summary
                  Text(
                    item.summary,
                    style: const TextStyle(fontSize: 16),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 16),

                  // Get voucher button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: onTap,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF0093EE),
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Xem chi tiết',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
