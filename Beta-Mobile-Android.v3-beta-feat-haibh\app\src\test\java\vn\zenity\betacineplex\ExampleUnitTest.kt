package vn.zenity.betacineplex

import io.reactivex.Scheduler
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.schedulers.Schedulers
import org.junit.Assert
import org.junit.Test

import org.junit.Assert.*
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.Manager.Network.CityAPI
import java.util.concurrent.CountDownLatch
import java.util.concurrent.TimeUnit

/**
 * Example local unit test, which will execute on the development machine (host).
 *
 * See [testing documentation](http://d.android.com/tools/testing).
 */
class ExampleUnitTest {
    @Test
    fun addition_isCorrect() {
        assertEquals(4, 2 + 2)
    }

    @Test
    fun getListCity(){
        val lock = CountDownLatch(1)
        val dispoable = APIClient.shared.cityAPI.getListCity()
                .subscribe({
                    response ->
                    lock.countDown()
                    Assert.assertNotNull(response.Data)
                    Assert.assertTrue(response.isSuccess)
                })

        lock.await(2000, TimeUnit.SECONDS)
    }

    @Test
    fun getListFilm(){
        val lock = CountDownLatch(1)
        val dispoable = APIClient.shared.filmAPI.getListFilm(true)
                .subscribe({response ->
                    lock.countDown()
                    Assert.assertNotNull(response.Data)
                    Assert.assertTrue(response.isSuccess)
                })
        lock.await()
    }
}
