import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:app_links/app_links.dart';

import '../utils/route_manager.dart';

/// Deep Link Service - Handles all deep link functionality exactly like iOS
/// Supports:
/// 1. Payment callbacks (MoMo, ZaloPay, AirPay) from native iOS/Android
/// 2. Push notification navigation
/// 3. In-app content links
class DeepLinkService {
  static final DeepLinkService _instance = DeepLinkService._internal();
  static DeepLinkService get instance => _instance;
  DeepLinkService._internal();

  final AppLinks _appLinks = AppLinks();
  StreamSubscription<Uri>? _linkSubscription;

  // Method channel for iOS native deep links - match native code
  static const MethodChannel _methodChannel = MethodChannel('deep_link_channel');

  // Method channel for Android deep links - match MainActivity
  static const MethodChannel _androidMethodChannel = MethodChannel('com.beta.betacineplex/deeplink');

  // Callbacks for payment status
  Function(PaymentCallbackData)? _onPaymentCallback;

  // Global navigator key for navigation
  GlobalKey<NavigatorState>? _navigatorKey;

  /// Initialize deep link service
  Future<void> initialize(GlobalKey<NavigatorState> navigatorKey) async {
    _navigatorKey = navigatorKey;

    try {
      // Setup method channel for iOS native deep links
      _methodChannel.setMethodCallHandler(_handleNativeDeepLink);

      // Setup method channel for Android deep links
      // _androidMethodChannel.setMethodCallHandler(_handleAndroidDeepLink);

      // Handle initial link if app was opened via deep link
      final Uri? initialLink = await _appLinks.getInitialLink();
      if (initialLink != null) {
        debugPrint('🔗 App opened with deep link: $initialLink');
        await _handleDeepLink(initialLink);
      }

      // Listen for incoming links when app is already running
      _linkSubscription = _appLinks.uriLinkStream.listen(
        (Uri uri) {
          debugPrint('🔗 Deep link received: $uri');
          _handleDeepLink(uri);
        },
        onError: (err) {
          debugPrint('❌ Deep link error: $err');
        },
      );

      debugPrint('✅ Deep Link Service initialized');
    } catch (e) {
      debugPrint('❌ Failed to initialize Deep Link Service: $e');
    }
  }

  /// Handle native deep links from iOS AppDelegate
  Future<void> _handleNativeDeepLink(MethodCall call) async {
    debugPrint('🔗 Native deep link received: ${call.method}');

    if (call.method == 'onDeepLink') {
      final Map<String, dynamic> arguments = Map<String, dynamic>.from(call.arguments);
      final String? type = arguments['type'];
      final String? url = arguments['url'];

      debugPrint('🔗 Processing native deep link: $type - $url');

      switch (type) {
        case 'momo_payment':
          await _handleNativeMomoCallback(arguments);
          break;
        case 'zalopay_payment':
          await _handleNativeZaloPayCallback(arguments);
          break;
        case 'shopeepay_payment':
          await _handleNativeShopeePayCallback(arguments);
          break;
        case 'navigation':
          if (url != null) {
            await _handleDeepLink(Uri.parse(url));
          }
          break;
        default:
          debugPrint('❓ Unknown native deep link type: $type');
      }
    }
  }

  /// Handle native MoMo callback from iOS
  Future<void> _handleNativeMomoCallback(Map<String, dynamic> data) async {
    debugPrint('💳 Processing native MoMo callback: $data');

    final paymentData = PaymentCallbackData(
      type: PaymentType.momo,
      orderId: data['orderId'],
      resultCode: data['resultCode'],
      requestId: data['requestId'],
      transId: data['transId'],
      message: data['message'],
      responseTime: data['responseTime'],
      payType: data['payType'],
      extraData: data['extraData'],
      partnerCode: data['partnerCode'],
    );

    _onPaymentCallback?.call(paymentData);
  }

  /// Handle native ZaloPay callback from iOS
  Future<void> _handleNativeZaloPayCallback(Map<String, dynamic> data) async {
    debugPrint('💳 Processing native ZaloPay callback: $data');

    final paymentData = PaymentCallbackData(
      type: PaymentType.zalopay,
      appId: data['appid'],
      appTransId: data['apptransid'],
      pmcId: data['pmcid'],
      bankCode: data['bankcode'],
      amount: data['amount'],
      discountAmount: data['discountamount'],
      status: data['status'],
      checksum: data['checksum'],
    );

    _onPaymentCallback?.call(paymentData);
  }

  /// Handle native ShopeePay callback from iOS
  Future<void> _handleNativeShopeePayCallback(Map<String, dynamic> data) async {
    debugPrint('💳 Processing native ShopeePay callback: $data');

    final paymentData = PaymentCallbackData(
      type: PaymentType.shopeepay,
      orderId: data['orderId'],
      status: data['status'],
      amount: data['amount'],
      message: data['message'],
    );

    _onPaymentCallback?.call(paymentData);
  }

  /// Set payment callback handler
  void setPaymentCallback(Function(PaymentCallbackData) callback) {
    _onPaymentCallback = callback;
  }

  /// Handle incoming deep link
  Future<void> _handleDeepLink(Uri uri) async {
    try {
      final String scheme = uri.scheme;
      final String host = uri.host;
      final String path = uri.path;
      final Map<String, String> params = uri.queryParameters;

      debugPrint('🔗 Processing deep link:');
      debugPrint('  - Scheme: $scheme');
      debugPrint('  - Host: $host');
      debugPrint('  - Path: $path');
      debugPrint('  - Params: $params');

      // Handle different types of deep links
      if (scheme == 'betacineplexx') {
        await _handleBetaCineplexLink(host, path, params);
      } else if (scheme == 'fb367174740769877') {
        await _handleFacebookLink(uri);
      } else {
        debugPrint('❓ Unknown deep link scheme: $scheme');
      }
    } catch (e) {
      debugPrint('❌ Error handling deep link: $e');
    }
  }

  /// Handle Beta Cineplex deep links
  Future<void> _handleBetaCineplexLink(String host, String path, Map<String, String> params) async {
    switch (host) {
      case 'payment':
        await _handlePaymentCallback(params);
        break;
      case 'route':
        await _handleRouteNavigation(path, params);
        break;
      case 'news':
        await _handleNewsLink(path);
        break;
      case 'voucher':
        await _handleVoucherLink(path);
        break;
      default:
        debugPrint('❓ Unknown Beta Cineplex host: $host');
    }
  }

  /// Handle payment callbacks exactly like iOS
  Future<void> _handlePaymentCallback(Map<String, String> params) async {
    debugPrint('💳 Processing payment callback: $params');

    // Check for MoMo payment callback
    if (params.containsKey('orderId') && params.containsKey('resultCode')) {
      final paymentData = PaymentCallbackData(
        type: PaymentType.momo,
        orderId: params['orderId'],
        resultCode: params['resultCode'],
        requestId: params['requestId'],
        transId: params['transId'],
        message: params['message'],
        responseTime: params['responseTime'],
        payType: params['payType'],
        extraData: params['extraData'],
        partnerCode: params['partnerCode'],
      );

      debugPrint('💳 MoMo payment callback: ${paymentData.toString()}');
      _onPaymentCallback?.call(paymentData);
      return;
    }

    // Check for ZaloPay payment callback
    if (params.containsKey('appid') && params.containsKey('status')) {
      final paymentData = PaymentCallbackData(
        type: PaymentType.zalopay,
        appId: params['appid'],
        appTransId: params['apptransid'],
        pmcId: params['pmcid'],
        bankCode: params['bankcode'],
        amount: params['amount'],
        discountAmount: params['discountamount'],
        status: params['status'],
        checksum: params['checksum'],
      );

      debugPrint('💳 ZaloPay payment callback: ${paymentData.toString()}');
      _onPaymentCallback?.call(paymentData);
      return;
    }

    // Check for ShopeePay payment callback
    if (params.containsKey('shopee_order_id') || params.containsKey('shopee_status')) {
      final paymentData = PaymentCallbackData(
        type: PaymentType.shopeepay,
        orderId: params['shopee_order_id'] ?? params['orderId'],
        status: params['shopee_status'] ?? params['status'],
        amount: params['amount'],
        message: params['message'],
      );

      debugPrint('💳 ShopeePay payment callback: ${paymentData.toString()}');
      _onPaymentCallback?.call(paymentData);
      return;
    }

    debugPrint('❓ Unknown payment callback format');
  }

  /// Handle route navigation exactly like iOS RouteManager
  Future<void> _handleRouteNavigation(String path, Map<String, String> params) async {
    final pathSegments = path.split('/').where((s) => s.isNotEmpty).toList();

    if (pathSegments.length >= 2) {
      final int? routeTypeValue = int.tryParse(pathSegments[0]);
      final String routeParam = pathSegments[1];

      if (routeTypeValue != null) {
        final RouteType routeType = RouteType.fromValue(routeTypeValue);

        if (_navigatorKey?.currentContext != null) {
          final routeManager = RouteManager(
            context: _navigatorKey!.currentContext!,
            type: routeType,
            params: [routeParam],
          );

          debugPrint('🧭 Navigating via RouteManager: $routeType with param: $routeParam');
          await routeManager.route();
        }
      }
    }
  }

  /// Handle news deep link
  Future<void> _handleNewsLink(String path) async {
    final newsId = path.replaceFirst('/', '');
    if (newsId.isNotEmpty && _navigatorKey?.currentContext != null) {
      final routeManager = RouteManager(
        context: _navigatorKey!.currentContext!,
        type: RouteType.promotionDetail,
        params: [newsId],
      );
      await routeManager.route();
    }
  }

  /// Handle voucher deep link
  Future<void> _handleVoucherLink(String path) async {
    final voucherId = path.replaceFirst('/', '');
    if (voucherId.isNotEmpty && _navigatorKey?.currentContext != null) {
      final routeManager = RouteManager(
        context: _navigatorKey!.currentContext!,
        type: RouteType.voucherDetail,
        params: [voucherId],
      );
      await routeManager.route();
    }
  }

  /// Handle Facebook deep link
  Future<void> _handleFacebookLink(Uri uri) async {
    debugPrint('📘 Facebook deep link: $uri');
    // Handle Facebook login callback if needed
  }

  /// Dispose resources
  void dispose() {
    _linkSubscription?.cancel();
    _linkSubscription = null;
  }
}

/// Payment callback data model
class PaymentCallbackData {
  final PaymentType type;

  // MoMo fields
  final String? orderId;
  final String? resultCode;
  final String? requestId;
  final String? transId;
  final String? message;
  final String? responseTime;
  final String? payType;
  final String? extraData;
  final String? partnerCode;

  // ZaloPay fields
  final String? appId;
  final String? appTransId;
  final String? pmcId;
  final String? bankCode;
  final String? amount;
  final String? discountAmount;
  final String? status;
  final String? checksum;

  PaymentCallbackData({
    required this.type,
    this.orderId,
    this.resultCode,
    this.requestId,
    this.transId,
    this.message,
    this.responseTime,
    this.payType,
    this.extraData,
    this.partnerCode,
    this.appId,
    this.appTransId,
    this.pmcId,
    this.bankCode,
    this.amount,
    this.discountAmount,
    this.status,
    this.checksum,
  });

  @override
  String toString() {
    return 'PaymentCallbackData(type: $type, orderId: $orderId, resultCode: $resultCode, status: $status)';
  }
}

/// Payment type enum
enum PaymentType {
  momo,
  zalopay,
  airpay,
  shopeepay,
}
