package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.PaymentHistory
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class BookHistoryPresenter : BookHistoryContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getPaymentHistory(accountId: String) {
        disposable = APIClient.shared.transactionAPI.getPaymentHistory(accountId).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        view?.get()?.showPaymentHistory(it.Data ?: listOf())
                    } else {
                        view?.get()?.showAlert(it.Message ?: R.string.get_payment_history_error.getString())
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.showAlert(it.message ?: R.string.get_payment_history_error.getString())
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<BookHistoryContractor.View?>? = null
    override fun attachView(view: BookHistoryContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
