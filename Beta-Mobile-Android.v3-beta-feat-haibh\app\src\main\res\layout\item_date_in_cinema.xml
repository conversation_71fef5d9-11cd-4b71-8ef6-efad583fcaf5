<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="68dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingLeft="@dimen/margin_normal"
    android:background="?attr/selectableItemBackground">
    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDate"
        android:layout_width="58dp"
        android:layout_height="wrap_content"
        tools:text="05"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:textColor="@color/textGray"
        android:textSize="28sp"
        app:fontFamily="@font/oswald_regular"
        android:gravity="center"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDay"
        android:layout_width="58dp"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_height="wrap_content"
        android:text="@string/today"
        android:textSize="@dimen/font_normal"
        android:textColor="@color/textGray"
        app:fontFamily="@font/oswald_regular"
        app:layout_constraintTop_toBottomOf="@+id/tvDate"
        android:gravity="center"/>
</androidx.constraintlayout.widget.ConstraintLayout>