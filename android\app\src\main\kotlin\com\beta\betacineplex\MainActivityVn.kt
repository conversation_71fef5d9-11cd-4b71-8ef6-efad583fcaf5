package com.beta.betacineplex

import android.app.Activity
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import com.betacineplex.SignalRPlugin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivityVn : FlutterActivity() {
    private val CHANNEL = "com.beta.betacineplex/payment"  // ✅ Updated channel name
    private val DEEP_LINK_CHANNEL = "com.beta.betacineplex/deeplink"  // ✅ Deep link channel
    // 🚫 PAYMENT_REQUEST_CODE and pendingResult removed - no longer needed
    private var deepLinkMethodChannel: MethodChannel? = null

    override fun configureFlutterEngine(@NonNull flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        // Register the SignalR plugin
        flutterEngine.plugins.add(SignalRPlugin())

        // Register payment WebView method channel
        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL).setMethodCallHandler { call, result ->
            when (call.method) {
                "launchPaymentWebView" -> {
                    val bookingData = call.argument<String>("bookingData")
                    launchPaymentWebView(bookingData, result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        // Register deep link method channel for MoMo payment return
        deepLinkMethodChannel = MethodChannel(flutterEngine.dartExecutor.binaryMessenger, DEEP_LINK_CHANNEL)
    }

    private fun launchPaymentWebView(bookingData: String?, result: MethodChannel.Result) {
        if (bookingData == null) {
            result.error("INVALID_ARGUMENT", "Booking data is required", null)
            return
        }

        Log.d("MainActivityVn", "🚫 PaymentWebViewActivity removed - using Flutter WebView instead")

        // Since PaymentWebViewActivity is removed, return error to use Flutter WebView
        result.error("NATIVE_WEBVIEW_UNAVAILABLE", "Native WebView removed, use Flutter WebView instead", null)
    }

    // 🚫 onActivityResult removed - no longer needed without PaymentWebViewActivity

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        handleDeepLink(intent)
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        setIntent(intent)
        handleDeepLink(intent)
    }

    private fun handleDeepLink(intent: Intent?) {
        intent?.data?.let { uri ->
            Log.d("MainActivityVn", "🔗 Deep link received: $uri")
            Log.d("MainActivityVn", "🔗 Scheme: ${uri.scheme}, Host: ${uri.host}")
            Log.d("MainActivityVn", "🔗 Full URI: ${uri.toString()}")

            // Validate deep link format
            if (uri.scheme.isNullOrEmpty() || uri.host.isNullOrEmpty()) {
                Log.e("MainActivityVn", "❌ Invalid deep link format - missing scheme or host")
                return@let
            }

            // Check for malformed URLs (with spaces or wrong scheme)
            val uriString = uri.toString()
            if (uriString.contains(" ") || uriString.contains("betacineplex ://")) {
                Log.e("MainActivityVn", "❌ Malformed deep link URL detected: $uriString")
                return@let
            }

            if (uri.scheme == "betacineplexx") {
                when (uri.host) {
                    "momo" -> {
                        Log.d("MainActivityVn", "💳 MoMo payment return detected")

                        // Extract MoMo payment parameters exactly like Android repo
                        val orderId = uri.getQueryParameter("orderId")
                        val resultCode = uri.getQueryParameter("resultCode")
                        val requestId = uri.getQueryParameter("requestId")
                        val transId = uri.getQueryParameter("transId")
                        val message = uri.getQueryParameter("message")
                        val responseTime = uri.getQueryParameter("responseTime")
                        val payType = uri.getQueryParameter("payType")
                        val extraData = uri.getQueryParameter("extraData")
                        val partnerCode = uri.getQueryParameter("partnerCode")

                        Log.d("MainActivityVn", "📱 MoMo parameters:")
                        Log.d("MainActivityVn", "   - orderId: $orderId")
                        Log.d("MainActivityVn", "   - resultCode: $resultCode")
                        Log.d("MainActivityVn", "   - transId: $transId")

                        // Send MoMo parameters to Flutter exactly like Android repo BroadcastReceiver
                        try {
                            deepLinkMethodChannel?.invokeMethod("onMoMoPaymentReturn", mapOf(
                                "orderId" to orderId,
                                "resultCode" to resultCode,
                                "requestId" to requestId,
                                "transId" to transId,
                                "message" to message,
                                "responseTime" to responseTime,
                                "payType" to payType,
                                "extraData" to extraData,
                                "partnerCode" to partnerCode
                            ))
                            Log.d("MainActivityVn", "✅ MoMo payment data sent to Flutter successfully")
                        } catch (e: Exception) {
                            Log.e("MainActivityVn", "❌ Error sending MoMo payment data to Flutter: $e")
                        }
                    }
                    "zalopay" -> {
                        Log.d("MainActivityVn", "💳 ZaloPay payment return detected")

                        // Extract ZaloPay payment parameters
                        val appTransId = uri.getQueryParameter("apptransid")
                        val status = uri.getQueryParameter("status")
                        val amount = uri.getQueryParameter("amount")

                        Log.d("MainActivityVn", "📱 ZaloPay parameters:")
                        Log.d("MainActivityVn", "   - appTransId: $appTransId")
                        Log.d("MainActivityVn", "   - status: $status")

                        // Send ZaloPay parameters to Flutter
                        deepLinkMethodChannel?.invokeMethod("onZaloPayPaymentReturn", mapOf(
                            "appTransId" to appTransId,
                            "status" to status,
                            "amount" to amount
                        ))
                    }
                    "airpay" -> {
                        Log.d("MainActivityVn", "💳 AirPay payment return detected")

                        // Extract AirPay payment parameters
                        val orderId = uri.getQueryParameter("order_id")
                        val status = uri.getQueryParameter("status")

                        Log.d("MainActivityVn", "📱 AirPay parameters:")
                        Log.d("MainActivityVn", "   - orderId: $orderId")
                        Log.d("MainActivityVn", "   - status: $status")

                        // Send AirPay parameters to Flutter
                        deepLinkMethodChannel?.invokeMethod("onAirPayPaymentReturn", mapOf(
                            "orderId" to orderId,
                            "status" to status
                        ))
                    }
                    "shopeepay" -> {
                        Log.d("MainActivityVn", "💳 ShopeePay payment return detected")

                        // Extract ShopeePay payment parameters
                        val orderId = uri.getQueryParameter("orderId") ?: uri.getQueryParameter("order_id")
                        val status = uri.getQueryParameter("status")
                        val amount = uri.getQueryParameter("amount")

                        Log.d("MainActivityVn", "📱 ShopeePay parameters:")
                        Log.d("MainActivityVn", "   - orderId: $orderId")
                        Log.d("MainActivityVn", "   - status: $status")
                        Log.d("MainActivityVn", "   - amount: $amount")

                        // Send ShopeePay parameters to Flutter
                        deepLinkMethodChannel?.invokeMethod("onShopeePayPaymentReturn", mapOf(
                            "orderId" to orderId,
                            "status" to status,
                            "amount" to amount
                        ))
                    }
                    else -> {
                        Log.d("MainActivityVn", "❓ Unknown deep link host: ${uri.host}")
                    }
                }
            }
        }
    }
}
