enum SeatSoldStatus {
  WAITING, // -1: chờ QR xử lý
  SELECTING, // 0: ghế đang chọn
  EMPTY, // 1: ghế trống
  SELECTED, // 2: ghế đang được người khác giữ
  BOOKED, // 3: ghế đã đặt chưa thanh toán
  SOLD, // 4: ghế đã thanh toán
  SOLDED; // 5: ghế đã thanh toán + lấy vé

  static SeatSoldStatus? fromInt(int? value) {
    if (value == null) return null;
    switch (value) {
      case -1:
        return SeatSoldStatus.WAITING;
      case 0:
        return SeatSoldStatus.SELECTING;
      case 1:
        return SeatSoldStatus.EMPTY;
      case 2:
        return SeatSoldStatus.SELECTED;
      case 3:
        return SeatSoldStatus.BOOKED;
      case 4:
        return SeatSoldStatus.SOLD;
      case 5:
        return SeatSoldStatus.SOLDED;
      default:
        return null;
    }
  }

  int toInt() {
    switch (this) {
      case SeatSoldStatus.WAITING:
        return -1;
      case SeatSoldStatus.SELECTING:
        return 0;
      case SeatSoldStatus.EMPTY:
        return 1;
      case SeatSoldStatus.SELECTED:
        return 2;
      case SeatSoldStatus.BOOKED:
        return 3;
      case SeatSoldStatus.SOLD:
        return 4;
      case SeatSoldStatus.SOLDED:
        return 5;
    }
  }
}

enum SeatType {
  NORMAL,
  VIP,
  COUPLE;

  static SeatType fromString(String? type) {
    if (type == null) return SeatType.NORMAL;
    switch (type.toLowerCase()) {
      case 'vip':
        return SeatType.VIP;
      case 'couple':
      case 'double':
        return SeatType.COUPLE;
      default:
        return SeatType.NORMAL;
    }
  }
}

class SeatTypeModel {
  final String? name;
  final String? value;
  final String? background;

  SeatTypeModel({
    this.name,
    this.value,
    this.background,
  });

  factory SeatTypeModel.fromJson(Map<String, dynamic> json) {
    return SeatTypeModel(
      name: json['Name'],
      value: json['Value'],
      background: json['Background'],
    );
  }

  bool get isNormal => value == 'c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b';
  bool get isVip => value == '9f2dda7f-d09e-4d58-a504-5a6311345aae';
  bool get isCouple => value == '9beee28c-8cae-41d0-bd01-b0b22108432c';
}
class StatusSeat {
  final String? name;
  final String? value;
  final String? Class;

  StatusSeat({
    this.name,
    this.value,
    this.Class,
  });

  factory StatusSeat.fromJson(Map<String, dynamic> json) {
    return StatusSeat(
      name: json['Name'],
      value: json['Value'],
      Class: json['Class'],
    );
  }

  // ✅ Match iOS StatusSeat properties
  bool get isWay => Class == 'way';
  bool get isBroken => Class == 'broken';
  bool get isUsed => Class == 'used';
}

class SeatModel {
  final String? seatNumber;
  final StatusSeat? status;
  final String? classStyle;
  final int? seatIndex;
  SeatSoldStatus? soldStatus;
  final SeatTypeModel? seatType;
  SeatModel? coupleSeat; // Added coupleSeat property
  bool isShowDouble ; // Indicates if this seat is part of a couple seat

  SeatModel({
    this.seatNumber,
    this.classStyle,
    this.soldStatus,
    this.status,
    this.seatIndex,
    this.seatType,
    this.coupleSeat,
    this.isShowDouble = false,
  });

  factory SeatModel.fromJson(Map<String, dynamic> json) {
    return SeatModel(
      seatNumber: json['SeatName'],
      status: json['Status'] != null
          ? StatusSeat.fromJson(json['Status'])
          : null,
      seatType: json['SeatType'] != null
          ? SeatTypeModel.fromJson(json['SeatType'])
          : null,
      seatIndex: json['SeatIndex'] as int?,
      soldStatus: SeatSoldStatus.fromInt(json['SoldStatus'] as int?),
    );
  }

  SeatModel copyWith({
    String? seatNumber,
    String? classStyle,
    StatusSeat? status,
    int? seatIndex,
    SeatSoldStatus? soldStatus,
    SeatTypeModel? seatType,
    SeatModel? coupleSeat,
    bool? isShowDouble,
  }) {
    return SeatModel(
      seatNumber: seatNumber ?? this.seatNumber,
      classStyle: classStyle ?? this.classStyle,
      status: status ?? this.status,
      seatIndex: seatIndex ?? this.seatIndex,
      soldStatus: soldStatus ?? this.soldStatus,
      seatType: seatType ?? this.seatType,
      coupleSeat: coupleSeat ?? this.coupleSeat,isShowDouble: isShowDouble ?? this.isShowDouble,
    );
  }

  bool get isWay => status?.value == '2';
  bool get isBroken => status?.value == '3';
  bool get isNotUsed => status?.value == '0';
  bool get isUsed => status?.value == '1';
  bool get isEntranceExit => status?.value == '4';

  SeatType get seatTypeEnum {
    if (seatType?.isVip == true) {
      return SeatType.VIP;
    } else if (seatType?.isCouple == true) {
      return SeatType.COUPLE;
    } else {
      return SeatType.NORMAL;
    }
  }
}
