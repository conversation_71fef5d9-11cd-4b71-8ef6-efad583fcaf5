<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:paddingTop="@dimen/padding_large"
    android:paddingLeft="@dimen/padding_large"
    android:paddingRight="@dimen/padding_large">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintGuide_percent="0.28"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintGuide_percent="0.72"
        app:layout_constraintTop_toTopOf="parent" />



    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/gl1"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingTop="10dp"
        android:paddingBottom="10dp"
        android:background="#e9e9e9"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/card_name"
        android:gravity="center"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_bold"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="@+id/gl1"
        app:layout_constraintRight_toRightOf="@+id/gl2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="#e9e9e9"
        android:padding="5dp"
        android:text="@string/so_the"
        android:gravity="center"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_bold"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="@+id/gl2"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="#e9e9e9"
        android:padding="5dp"
        android:text="@string/card_register"
        android:gravity="center"
        android:textColor="@color/textBlack"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_bold"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/grayLine"/>
    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>
    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="@+id/gl1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>

    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="@+id/gl2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>

    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>
</androidx.constraintlayout.widget.ConstraintLayout>