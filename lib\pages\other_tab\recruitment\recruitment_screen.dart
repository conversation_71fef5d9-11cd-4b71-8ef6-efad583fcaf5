import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/other_tab/recruitment/recruitment_detail_screen.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

class RecruitmentScreen extends StatefulWidget {
  const RecruitmentScreen({super.key});

  @override
  State<RecruitmentScreen> createState() => _RecruitmentScreenState();
}

class _RecruitmentScreenState extends State<RecruitmentScreen> {
  bool _isLoading = true;
  List<PromotionItem> _recruitments = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchRecruitments();
  }

  Future<void> _fetchRecruitments() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get recruitment information
      final result = await RepositoryProvider.of<Api>(context).other.getRecruitment(
        pageSize: 20,
        pageNumber: 1
      );

      if (result != null) {
        final List<dynamic> recruitmentData = result.data['content'] ?? [];
        setState(() {
          _recruitments = recruitmentData.map((item) => PromotionItem.fromJson(item)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load recruitment data';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'Other.Recruitment'.tr(),
        titleColor: Colors.white
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _fetchRecruitments,
              child: Text('Bt.Retry'.tr()),
            ),
          ],
        ),
      );
    }

    if (_recruitments.isEmpty) {
      return  Center(child: Text('Recruitment.NoRecruitment'.tr()));
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: _recruitments.length,
      separatorBuilder: (context, index) => const Divider(),
      itemBuilder: (context, index) {
        final item = _recruitments[index];
        return RecruitmentItem(
          item: item,
          onTap: () => _navigateToDetail(item),
        );
      },
    );
  }

  void _navigateToDetail(PromotionItem item) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => RecruitmentDetailScreen(item: item),
      ),
    );
  }
}

class RecruitmentItem extends StatelessWidget {
  final PromotionItem item;
  final VoidCallback onTap;

  const RecruitmentItem({
    super.key,
    required this.item,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    // Format the date if available
    String formattedDate = '';
    if (item.publishDate != null) {
      try {
        final date = DateTime.parse(item.publishDate!);
        formattedDate = DateFormat('dd/MM/yyyy').format(date);
      } catch (e) {
        formattedDate = item.publishDate!;
      }
    }

    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: InkWell(
        onTap: onTap,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Title
              Text(
                item.title,
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Color(0xFF223849),
                ),
              ),
              const SizedBox(height: 8),

              // Date
              if (formattedDate.isNotEmpty)
                Text(
                  'Ngày đăng: $formattedDate',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey[600],
                  ),
                ),
              const SizedBox(height: 8),

              // Summary
              Text(
                item.summary,
                style: const TextStyle(fontSize: 16),
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
