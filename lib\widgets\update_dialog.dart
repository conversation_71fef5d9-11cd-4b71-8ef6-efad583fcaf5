import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';

import '../models/project/app_params.dart';
import '../service/version_manager.dart';

/// Update Dialog Widget - matches iOS/Android update notification dialogs
class UpdateDialog extends StatelessWidget {
  final VersionCheckResult result;
  final VoidCallback? onUpdatePressed;
  final VoidCallback? onLaterPressed;

  const UpdateDialog({
    super.key,
    required this.result,
    this.onUpdatePressed,
    this.onLaterPressed,
  });

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(
        result.isMandatory
            ? 'Update.RequiredTitle'.tr()
            : 'Update.AvailableTitle'.tr(),
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 18,
        ),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (result.message != null) ...[
            _buildMessageContent(context),
            const SizedBox(height: 16),
          ],
          _buildVersionInfo(context),
        ],
      ),
      actions: _buildActions(context),
    );
  }

  Widget _buildMessageContent(BuildContext context) {
    if (result.messageHighlight != null && result.message != null) {
      // Build highlighted message like Android showConfirmWithHighlight
      return Text(
        result.message!,
        style: const TextStyle(fontSize: 14,color: Colors.black),
      );
    } else

      if (result.message != null) {
      return Text(
        result.message!,
        style: const TextStyle(fontSize: 14,color: Colors.black),
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildVersionInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Update.CurrentVersion'.tr(),
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                result.currentVersion ?? 'Unknown',
                style: const TextStyle(fontSize: 14),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Update.LatestVersion'.tr(),
                style: const TextStyle(
                  fontWeight: FontWeight.w500,
                  fontSize: 14,
                ),
              ),
              Text(
                result.latestVersion ?? 'Unknown',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Colors.green,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  List<Widget> _buildActions(BuildContext context) {
    final List<Widget> actions = [];

    // Add "Later" button only if update is not mandatory
    if (!result.isMandatory && result.canClose) {
      actions.add(
        TextButton(
          onPressed: () {
            Navigator.of(context).pop();
            onLaterPressed?.call();
          },
          child: Text(
            'Update.Later'.tr(),
            style: TextStyle(
              color: Colors.grey.shade600,
            ),
          ),
        ),
      );
    }

    // Add "Update" button
    actions.add(
      ElevatedButton(
        onPressed: () {
          Navigator.of(context).pop();
          onUpdatePressed?.call();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: result.isMandatory ? Colors.red : Colors.blue,
          foregroundColor: Colors.white,
        ),
        child: Text(
          result.isMandatory
              ? 'Update.UpdateNow'.tr()
              : 'Update.Update'.tr(),
        ),
      ),
    );

    return actions;
  }

  /// Show update dialog
  static Future<void> show(
    BuildContext context,
    VersionCheckResult result, {
    VoidCallback? onUpdatePressed,
    VoidCallback? onLaterPressed,
  }) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: result.canClose && !result.isMandatory,
      builder: (BuildContext context) {
        return PopScope(
          canPop: result.canClose,
            child: UpdateDialog(
            result: result,
            onUpdatePressed: onUpdatePressed ?? () async {
              await VersionManager().openAppStore();
            },
            onLaterPressed: onLaterPressed ?? () async {
              if (result.latestVersion != null) {
                await VersionManager().dismissVersionCheck(result.latestVersion!);
              }
            },
          ),
        );
      },
    );
  }
}

/// Simple version info widget for settings screen
class VersionInfoWidget extends StatelessWidget {
  final String currentVersion;
  final String? latestVersion;
  final VersionStatus status;
  final VoidCallback? onCheckPressed;

  const VersionInfoWidget({
    super.key,
    required this.currentVersion,
    this.latestVersion,
    required this.status,
    this.onCheckPressed,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Version.AppVersion'.tr(),
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildVersionRow('Version.Current'.tr(), currentVersion),
            const SizedBox(height: 8),
            _buildStatusRow(context),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: onCheckPressed,
                child: Text('Version.CheckForUpdates'.tr()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVersionRow(String label, String version) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: const TextStyle(fontSize: 16),
        ),
        Text(
          version,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildStatusRow(BuildContext context) {
    String statusText;
    Color statusColor;
    IconData statusIcon;

    switch (status) {
      case VersionStatus.upToDate:
        statusText = 'Version.UpToDate'.tr();
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case VersionStatus.updateAvailable:
        statusText = 'Version.UpdateAvailable'.tr();
        statusColor = Colors.orange;
        statusIcon = Icons.update;
        break;
      case VersionStatus.updateRequired:
        statusText = 'Version.UpdateRequired'.tr();
        statusColor = Colors.red;
        statusIcon = Icons.warning;
        break;
      case VersionStatus.checkFailed:
        statusText = 'Version.CheckFailed'.tr();
        statusColor = Colors.grey;
        statusIcon = Icons.error_outline;
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          'Version.Status'.tr(),
          style: const TextStyle(fontSize: 16),
        ),
        Row(
          children: [
            Icon(
              statusIcon,
              color: statusColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              statusText,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: statusColor,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
