import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '/constants/index.dart';
import '/cubit/index.dart';
import '/service/notification_manager.dart';
import '/service/app_update_manager.dart';
import '/utils/index.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final _updateManager = AppUpdateManager();
  bool _hasCheckedVersion = false;

  @override
  void initState() {
    // initFirebase();
    context.read<AuthC>().check(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocConsumer<AuthC, AuthS>(
          listenWhen: (oldState, newState) => newState.status != AppStatus.init,
          listener: (context, state) {
            // Use WidgetsBinding to ensure widget tree is fully built
            // WidgetsBinding.instance.addPostFrameCallback((_) {
            //   if (mounted) {
            //     CSpace.setScreenSize(context);
            //     _navigateToHome();
            //   }
            // });
            Future.delayed(const Duration(milliseconds: 100),(){
              CSpace.setScreenSize(context);
              _navigateToHome();
            });

          },
          builder: (context, state) => Center(
                child: CIcon.logo,
              )),
    );
  }

  /// Navigate to home and check for app updates
  /// Matches iOS MainTabContainer.getAppParams() and Android HomeActivity.getAppParamsViaApi()
  Future<void> _navigateToHome() async {
    // Navigate to home first
    GoRouter.of(context).goNamed(CRoute.home);

    // Check for version updates after navigation
    // if (!_hasCheckedVersion) {
    //   _hasCheckedVersion = true;
    //   await _checkForUpdates();
    // }
  }

  /// Check for app updates and show dialog if needed
  /// This matches the iOS/Android behavior of checking version on app start
  Future<void> _checkForUpdates() async {
    try {
      // Small delay to ensure home screen is loaded
      await Future.delayed(const Duration(seconds: 2));

      if (!mounted) return;

      final api = RepositoryProvider.of<Api>(context);
      await _updateManager.initializeAndCheck(context, api);
    } catch (e) {
      debugPrint('Error checking for updates in splash: $e');
    }
  }

  /// Initialize Firebase and Notification - đã được move vào main.dart
  /// Method này giữ lại để tương thích, nhưng logic chính đã chuyển sang NotificationManager
  // Future<RemoteMessage?> initFirebase() async {
  //   try {
  //     // Firebase đã được initialize trong main.dart
  //     debugPrint('🔔 Firebase already initialized in main.dart');
  //
  //     // Initialize notification manager - tương ứng với iOS notification setup
  //     await NotificationManager.instance.initialize();
  //
  //     // Get initial message if app was opened from notification
  //     final RemoteMessage? message = await FirebaseMessaging.instance.getInitialMessage();
  //     if (message != null) {
  //       debugPrint('🔔 App opened from notification: ${message.messageId}');
  //       _handleMessage(message);
  //     }
  //
  //     return message;
  //   } catch (e) {
  //     debugPrint('❌ Error in initFirebase: $e');
  //   }
  //   return null;
  // }
  //
  // void _handleMessage(RemoteMessage message) {
  //   if (message.data['id'] != null && message.data['id'] != '') {
  //     // Navigator.pushNamedAndRemoveUntil(
  //     //   Utils.navigatorKey.currentState!.context,
  //     //   RoutesName.PROPERTY_SEE_DETAILS,
  //     //   arguments: message.data['id'],
  //     //       (route) => false,
  //     // );
  //   }
  // }
}
