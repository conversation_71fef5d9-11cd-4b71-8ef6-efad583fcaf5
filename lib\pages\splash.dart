import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '/constants/index.dart';
import '/cubit/index.dart';
import '/service/notification_manager.dart';
import '/service/app_update_manager.dart';
import '/utils/index.dart';

class SplashPage extends StatefulWidget {
  const SplashPage({super.key});

  @override
  State<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {
  final _updateManager = AppUpdateManager();

  @override
  void initState() {
    // initFirebase();
    context.read<AuthC>().check(context: context);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: BlocConsumer<AuthC, AuthS>(
          listenWhen: (oldState, newState) => newState.status != AppStatus.init,
          listener: (context, state) {
            // Use WidgetsBinding to ensure widget tree is fully built
            // WidgetsBinding.instance.addPostFrameCallback((_) {
            //   if (mounted) {
            //     CSpace.setScreenSize(context);
            //     _navigateToHome();
            //   }
            // });
            Future.delayed(const Duration(milliseconds: 100),(){
              CSpace.setScreenSize(context);
              _navigateToHome();
            });

          },
          builder: (context, state) => Center(
                child: CIcon.logo,
              )),
    );
  }

  Future<void> _navigateToHome() async {
    GoRouter.of(context).goNamed(CRoute.home);
  }
}
