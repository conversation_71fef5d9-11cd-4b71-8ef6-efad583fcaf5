# iOS Deep Link Setup Analysis & Fixes

## Tình trạng hiện tại

### ✅ **Đã setup đúng:**
1. **ios_style_webview_payment.dart**: ✅ Đã sử dụng DeepLinkService
2. **deep_link_service.dart**: ✅ <PERSON><PERSON> đầy đủ handler <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ShopeePay
3. **AppDelegate.swift**: ✅ Setup đúng như iOS repo với <PERSON>, ZaloPay handlers
4. **URL Scheme**: ✅ `betacineplexx` giống iOS repo

### ❌ **Vấn đề đã được sửa:**

#### **1. DeepLinkService chưa được initialize**
**Vấn đề**: DeepLinkService không được initialize trong main.dart
**Giải pháp**: 
```dart
// main.dart
import '/services/deep_link_service.dart';

class MyApp extends StatelessWidget {
  static final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  
  // In main()
  await DeepLinkService.instance.initialize(MyApp.navigatorKey);
  
  // In MaterialApp.router
  navigatorKey: navigator<PERSON>ey,
}
```

#### **2. Thiếu ShopeePay handler trong AppDelegate**
**Vấn đề**: AppDelegate.swift chỉ có MoMo và ZaloPay, thiếu ShopeePay
**Giải pháp**:
```swift
// AppDelegate.swift
// Handle ShopeePay payment callback exactly like iOS repository
if let orderId = url.valueOf("shopee_order_id"),
   let status = url.valueOf("shopee_status"),
   let amount = url.valueOf("amount"),
   let message = url.valueOf("message") {

  print("💳 iOS: ShopeePay payment callback received")
  NotificationCenter.default.post(
    name: NSNotification.Name("CheckShopeePayOrderStatus"),
    object: (orderId, status, amount, message)
  )

  // Forward to Flutter via method channel
  if let controller = window?.rootViewController as? FlutterViewController {
    let channel = FlutterMethodChannel(name: "deep_link_channel", binaryMessenger: controller.binaryMessenger)
    channel.invokeMethod("onDeepLink", arguments: [
      "url": url.absoluteString,
      "type": "shopeepay_payment",
      "orderId": orderId,
      "status": status,
      "amount": amount,
      "message": message
    ])
  }

  return true
}
```

#### **3. Thiếu LSApplicationQueriesSchemes cho payment apps**
**Vấn đề**: Info.plist thiếu schemes để query payment apps
**Giải pháp**:
```xml
<!-- Info.plist -->
<key>LSApplicationQueriesSchemes</key>
<array>
  <string>fbapi</string>
  <string>fb-messenger-share-api</string>
  <string>fbauth2</string>
  <string>fbshareextension</string>
  <!-- ✅ Add payment app schemes for deep link support -->
  <string>momo</string>
  <string>zalopay</string>
  <string>shopeepay</string>
  <string>airpay</string>
  <string>googlechromes</string>
  <string>comgooglemaps</string>
</array>
```

## So sánh với iOS Repo

### **iOS Repository (Beta-Mobile-iOS.v3-main):**
```swift
// AppDelegate.swift
func application(_ app: UIApplication, open url: URL, options: [UIApplicationOpenURLOptionsKey : Any] = [:]) -> Bool {
    // MoMo callback
    if let orderId = url.valueOf("orderId"), ... {
        NotificationCenter.default.post(name: NSNotification.Name.CheckMomoOrderStatus, object: (...))
        return false;
    }
    
    // ZaloPay callback  
    if let appid = url.valueOf("appid"), ... {
        NotificationCenter.default.post(name: NSNotification.Name.CheckZaloPayOrderStatus, object: (...))
        return false;
    }
    
    return ApplicationDelegate.shared.application(app, open: url, options: options)
}
```

### **Flutter Repository (Sau khi fix):**
```swift
// AppDelegate.swift
override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
    // MoMo callback - ✅ Giống iOS repo + forward to Flutter
    if let orderId = url.valueOf("orderId"), ... {
        NotificationCenter.default.post(name: NSNotification.Name("CheckMomoOrderStatus"), object: (...))
        
        // ✅ Forward to Flutter via method channel
        if let controller = window?.rootViewController as? FlutterViewController {
            let channel = FlutterMethodChannel(name: "deep_link_channel", binaryMessenger: controller.binaryMessenger)
            channel.invokeMethod("onDeepLink", arguments: [...])
        }
        return true
    }
    
    // ZaloPay callback - ✅ Giống iOS repo + forward to Flutter
    // ShopeePay callback - ✅ Thêm mới cho Flutter
    
    return super.application(app, open: url, options: options)
}
```

## Deep Link Flow

### **Correct Flow:**
```
MoMo/ZaloPay/ShopeePay App → Return to Flutter App → AppDelegate.swift
    ↓
Parse URL parameters → Post NotificationCenter (like iOS repo)
    ↓
Forward to Flutter via MethodChannel → DeepLinkService.dart
    ↓
Parse payment data → Call ios_style_webview_payment callback
    ↓
Execute JavaScript in WebView → Handle payment result
    ↓
Pop to home screen → Show success dialog
```

## URL Schemes Configuration

### **Info.plist (Đã đúng):**
```xml
<key>CFBundleURLTypes</key>
<array>
  <dict>
    <key>CFBundleURLName</key>
    <string>com.geneat.nmd.group</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>betacineplexx</string>  <!-- ✅ Giống iOS repo -->
    </array>
  </dict>
  <dict>
    <key>CFBundleTypeRole</key>
    <string>Editor</string>
    <key>CFBundleURLSchemes</key>
    <array>
      <string>fb367174740769877</string>  <!-- ✅ Facebook scheme -->
    </array>
  </dict>
</array>
```

## Payment Callback URLs

### **MoMo**: `betacineplexx://payment?orderId=...&resultCode=...`
### **ZaloPay**: `betacineplexx://payment?appid=...&status=...`
### **ShopeePay**: `betacineplexx://payment?shopee_order_id=...&shopee_status=...`

## Test Cases

### **Test Case 1: MoMo Payment**
1. User chọn MoMo trong WebView
2. App chuyển sang MoMo app
3. User thanh toán thành công
4. MoMo app callback: `betacineplexx://payment?orderId=123&resultCode=0`
5. **Expected**: AppDelegate → DeepLinkService → ios_style_webview_payment → Pop to home

### **Test Case 2: ZaloPay Payment**
1. User chọn ZaloPay trong WebView
2. App chuyển sang ZaloPay app
3. User thanh toán thành công
4. ZaloPay app callback: `betacineplexx://payment?appid=123&status=1`
5. **Expected**: Tương tự MoMo flow

### **Test Case 3: ShopeePay Payment**
1. User chọn ShopeePay trong WebView
2. App chuyển sang ShopeePay app
3. User thanh toán thành công
4. ShopeePay app callback: `betacineplexx://payment?shopee_order_id=123&shopee_status=success`
5. **Expected**: Tương tự MoMo flow

## Kết quả

### ✅ **Setup hoàn chỉnh:**
- **DeepLinkService**: ✅ Initialized in main.dart
- **AppDelegate**: ✅ Handle MoMo, ZaloPay, ShopeePay
- **Info.plist**: ✅ Correct URL schemes và LSApplicationQueriesSchemes
- **ios_style_webview_payment**: ✅ Proper callback handling
- **Navigation**: ✅ Pop to home after payment success

### 🔄 **Flow chính xác:**
```
Payment App → AppDelegate → DeepLinkService → ios_style_webview_payment → Home Screen
```

Với setup này, iOS deep link sẽ hoạt động chính xác như iOS repo gốc và user sẽ được pop về home screen sau khi thanh toán thành công!
