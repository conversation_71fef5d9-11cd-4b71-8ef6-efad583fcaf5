import UIKit
import Flutter
import Firebase
import GoogleMaps
import Foundation
import UserNotifications
// This is needed to access our SignalR classes

@main
@objc class AppDelegate: FlutterAppDelegate, MessagingDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    FirebaseApp.configure()
    Messaging.messaging().delegate = self
    // Flutter handles notification permissions and registration
    // Minimal setup - Flutter NotificationService handles the rest
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self
    }
    application.registerForRemoteNotifications()

    GMSServices.provideAPIKey("AIzaSyAO_SXaEFQ4QHOtFjk_zCQvy-Xa02SegyE")

    GeneratedPluginRegistrant.register(with: self)

    // Register SignalR plugin
    if let registrar = self.registrar(forPlugin: "SignalRPlugin") {
        SignalRPlugin.register(with: registrar)
    }

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // MARK: - Firebase Messaging Delegate

  func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
    print("🔔 FCM registration token: \(fcmToken ?? "nil")")

    let dataDict: [String: String] = ["token": fcmToken ?? ""]
    NotificationCenter.default.post(
      name: Notification.Name("FCMToken"),
      object: nil,
      userInfo: dataDict
    )
  }

  // MARK: - UNUserNotificationCenterDelegate
  // Note: FlutterAppDelegate already implements UNUserNotificationCenterDelegate
  // We just need to set the delegate in didFinishLaunchingWithOptions

  // MARK: - Deep Link Handling (exactly like iOS repository)
  override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
    print("🔗 iOS Deep Link received: \(url)")

    // Handle MoMo payment callback exactly like iOS repository
    if let orderId = url.valueOf("orderId"),
       let resultCode = url.valueOf("resultCode"),
       let requestId = url.valueOf("requestId"),
       let transId = url.valueOf("transId"),
       let message = url.valueOf("message"),
       let responseTime = url.valueOf("responseTime"),
       let payType = url.valueOf("payType"),
       let extraData = url.valueOf("extraData"),
       let partnerCode = url.valueOf("partnerCode") {

      print("💳 iOS: MoMo payment callback received")
      // Post notification exactly like iOS repository
      NotificationCenter.default.post(
        name: NSNotification.Name("CheckMomoOrderStatus"),
        object: (orderId, resultCode, requestId, transId, message, responseTime, payType, extraData, partnerCode)
      )

      // Forward to Flutter via method channel
      if let controller = window?.rootViewController as? FlutterViewController {
        let channel = FlutterMethodChannel(name: "deep_link_channel", binaryMessenger: controller.binaryMessenger)
        channel.invokeMethod("onDeepLink", arguments: [
          "url": url.absoluteString,
          "type": "momo_payment",
          "orderId": orderId,
          "resultCode": resultCode,
          "requestId": requestId,
          "transId": transId,
          "message": message,
          "responseTime": responseTime,
          "payType": payType,
          "extraData": extraData,
          "partnerCode": partnerCode
        ])
      }

      return true
    }

    // Handle ZaloPay payment callback exactly like iOS repository
    if let appid = url.valueOf("appid"),
       let appTransId = url.valueOf("apptransid"),
       let pmcid = url.valueOf("pmcid"),
       let bankCode = url.valueOf("bankcode"),
       let amount = url.valueOf("amount"),
       let discountAmount = url.valueOf("discountamount"),
       let status = url.valueOf("status"),
       let checksum = url.valueOf("checksum") {

      print("💳 iOS: ZaloPay payment callback received")
      // Post notification exactly like iOS repository
      NotificationCenter.default.post(
        name: NSNotification.Name("CheckZaloPayOrderStatus"),
        object: (appid, appTransId, pmcid, bankCode, amount, discountAmount, status, checksum)
      )

      // Forward to Flutter via method channel
      if let controller = window?.rootViewController as? FlutterViewController {
        let channel = FlutterMethodChannel(name: "deep_link_channel", binaryMessenger: controller.binaryMessenger)
        channel.invokeMethod("onDeepLink", arguments: [
          "url": url.absoluteString,
          "type": "zalopay_payment",
          "appid": appid,
          "apptransid": appTransId,
          "pmcid": pmcid,
          "bankcode": bankCode,
          "amount": amount,
          "discountamount": discountAmount,
          "status": status,
          "checksum": checksum
        ])
      }

      return true
    }

    // Handle ShopeePay payment callback exactly like iOS repository
    if let orderId = url.valueOf("shopee_order_id"),
       let status = url.valueOf("shopee_status"),
       let amount = url.valueOf("amount"),
       let message = url.valueOf("message") {

      print("💳 iOS: ShopeePay payment callback received")
      // Post notification exactly like iOS repository
      NotificationCenter.default.post(
        name: NSNotification.Name("CheckShopeePayOrderStatus"),
        object: (orderId, status, amount, message)
      )

      // Forward to Flutter via method channel
      if let controller = window?.rootViewController as? FlutterViewController {
        let channel = FlutterMethodChannel(name: "deep_link_channel", binaryMessenger: controller.binaryMessenger)
        channel.invokeMethod("onDeepLink", arguments: [
          "url": url.absoluteString,
          "type": "shopeepay_payment",
          "orderId": orderId,
          "status": status,
          "amount": amount,
          "message": message
        ])
      }

      return true
    }

    // Handle other deep links (navigation, content, etc.)
    if url.scheme == "betacineplexx" {
      print("🔗 iOS: Beta Cineplex deep link")
      if let controller = window?.rootViewController as? FlutterViewController {
        let channel = FlutterMethodChannel(name: "deep_link_channel", binaryMessenger: controller.binaryMessenger)
        channel.invokeMethod("onDeepLink", arguments: [
          "url": url.absoluteString,
          "type": "navigation"
        ])
      }
      return true
    }

    // Handle Facebook deep links
    if url.scheme == "fb367174740769877" {
      print("📘 iOS: Facebook deep link")
      // Let Flutter handle Facebook login
      return super.application(app, open: url, options: options)
    }

    // Default handling
    return super.application(app, open: url, options: options)
  }
}

// MARK: - URL Extension (exactly like iOS repository)
extension URL {
  func valueOf(_ queryParameterName: String) -> String? {
    guard let url = URLComponents(string: self.absoluteString) else { return nil }
    return url.queryItems?.first(where: { $0.name == queryParameterName })?.value
  }
}
