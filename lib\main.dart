import 'dart:async';
import 'dart:async';
import 'dart:io';

import 'package:easy_localization/easy_localization.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';


import '/constants/index.dart';
import '/cubit/index.dart';
import '/service/language_service.dart';
import '/service/notification_service.dart';
import '/services/remote_config_icon_service.dart';
import '/services/simple_remote_config_test.dart';
import '/utils/index.dart';
import '/widgets/remote_config_icon_manager.dart';
import 'firebase_options.dart';

/// Background message handler - tương ứng với iOS background notification handling
@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // Initialize Firebase if not already initialized
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
}

/// Initialize Remote Config Icon Service asynchronously
Future<void> _initRemoteConfigIconService() async {
  try {
    debugPrint('🔄 Start init remote config icon service...');

    // Add small delay to ensure Firebase is fully ready
    await Future.delayed(const Duration(milliseconds: 500));

    // First test simple Remote Config
    debugPrint('🔄 Testing simple Remote Config...');
    await SimpleRemoteConfigTest.testRemoteConfig();
    debugPrint('✅ Simple Remote Config test completed');

    // Then try full icon service
    final iconService = RemoteConfigIconService();
    debugPrint('🔄 Initializing icon service...');

    // Add timeout to prevent hanging
    await iconService.initialize().timeout(
      const Duration(seconds: 30),
      onTimeout: () {
        debugPrint('⏰ Icon service initialization timed out');
        throw TimeoutException('Icon service initialization timed out', const Duration(seconds: 30));
      },
    );
    debugPrint('✅ Icon service initialized, fetching config...');

    // Fetch and apply icon immediately on app start
    await iconService.fetchAndApplyIcon().timeout(
      const Duration(seconds: 15),
      onTimeout: () {
        debugPrint('⏰ Icon config fetch timed out');
        throw TimeoutException('Icon config fetch timed out', const Duration(seconds: 15));
      },
    );
    debugPrint('✅ Remote Config Icon Service initialized successfully');

  } catch (e, stackTrace) {
    debugPrint('❌ Remote Config Icon Service initialization failed: $e');
    debugPrint('Stack trace: $stackTrace');
    // Continue app initialization even if icon service fails
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await dotenv.load(fileName: Environment.fileName);
  // HttpOverrides.global = MyHttpOverrides();
  await EasyLocalization.ensureInitialized();

  // Initialize Firebase - tương ứng với iOS Firebase setup
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Set background message handler - tương ứng với iOS background handling
  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

  // ✅ Initialize Remote Config Icon Service (non-blocking)
  _initRemoteConfigIconService();

  // Initialize unified notification service - CROSS-PLATFORM approach
  // Replaces iOS AppDelegate notification setup and Android FirebaseMessagingService
  try {
    await NotificationService.instance.requestPermissions();
  } catch (e) {
    // ✅ Android 12+ compatibility: Don't crash if notification service fails
    debugPrint('⚠️ NotificationService initialization failed: $e');
    // Continue app initialization even if notifications fail
  }

  // Get saved language preference
  final languageService = LanguageService();
  final savedLanguage = await languageService.getCurrentLanguage();

  runApp(EasyLocalization(
      supportedLocales: const [
        Locale('en'),
        Locale('vi'),
      ],
      path: 'assets/translations',
      fallbackLocale: const Locale('vi'),
      startLocale: Locale(savedLanguage), // Use saved language
      child: MyApp()));

  // unawaited(_initRemoteConfigIconService());

}
Future<void> _initRemoteConfigIconService() async {
  debugPrint('🚀 Start init Remote Config Icon Service');
  try {
    final iconService = RemoteConfigIconService();
    await iconService.initialize();
    await iconService.fetchAndApplyIcon();
    debugPrint('✅ Remote Config Icon Service initialized successfully');
  } catch (e, st) {
    debugPrint('⚠️ Remote Config Icon Service failed: $e');
  }
}

class MyHttpOverrides extends HttpOverrides{
  @override
  HttpClient createHttpClient(SecurityContext? context){
    return super.createHttpClient(context)
      ..badCertificateCallback = (X509Certificate cert, String host, int port)=> true;
  }
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  final Api _api = Api();

  @override
  Widget build(BuildContext context) {
    return RepositoryProvider.value(
        value: _api,
        child: MultiBlocProvider(
          providers: [
            BlocProvider<AuthC>(
              create: (BuildContext context) => AuthC(),
            ),
          ],
          child: MaterialApp.router(
          title: 'Beta Cinema',
          builder: (BuildContext context, Widget? child) => MediaQuery(
            data:
                MediaQuery.of(context).copyWith(alwaysUse24HourFormat: true),
            child: GestureDetector(
              onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
              child: child,
            ),
          ),

          debugShowCheckedModeBanner: false,
          localizationsDelegates: context.localizationDelegates,
          supportedLocales: context.supportedLocales,
          locale: context.locale,
          theme: ThemeData(
            useMaterial3: false,
            fontFamily: 'SourceSansPro', // Default font should be SourceSansPro like iOS/Android
            dialogTheme: const DialogTheme(
              titleTextStyle: TextStyle(
                fontFamily: 'SourceSansPro',
                color: Colors.black,
              ),
              contentTextStyle: TextStyle(
                fontFamily: 'SourceSansPro',
                color: Colors.black,
              ),
            ),
            textTheme: const TextTheme(
              // Title styles use Oswald like iOS
              headlineLarge: TextStyle(
                fontFamily: 'Oswald',
                fontSize: CFontSize.xl3,
                fontWeight: FontWeight.w600,
              ),
              headlineMedium: TextStyle(
                fontFamily: 'Oswald',
                fontSize: CFontSize.xl2,
                fontWeight: FontWeight.w600,
              ),
              headlineSmall: TextStyle(
                fontFamily: 'Oswald',
                fontSize: CFontSize.xl,
                fontWeight: FontWeight.w600,
              ),
              // Body text uses SourceSansPro like iOS/Android
              bodyLarge: TextStyle(
                fontFamily: 'SourceSansPro',
                fontSize: CFontSize.base,
              ),
              bodyMedium: TextStyle(
                fontSize: CFontSize.sm,
                fontWeight: FontWeight.w400,
                textBaseline: TextBaseline.alphabetic,
                fontFamily: 'SourceSansPro',
                height: 1.1,
              ),
              bodySmall: TextStyle(
                fontFamily: 'SourceSansPro',
                fontSize: CFontSize.xs,
              ),
              labelLarge: TextStyle(
                  fontSize: CFontSize.base, fontWeight: FontWeight.w400),
            ),
            scaffoldBackgroundColor: Colors.white,
            primarySwatch: CColor.blue,
            unselectedWidgetColor: CColor.blue,
            floatingActionButtonTheme: FloatingActionButtonThemeData(
                backgroundColor: CColor.primary,
                foregroundColor: Colors.white),
            elevatedButtonTheme:
                ElevatedButtonThemeData(style: CStyle.button),
          ),
          routeInformationProvider: routes.routeInformationProvider,
          routeInformationParser: routes.routeInformationParser,
          routerDelegate: routes.routerDelegate,
          ),
        ));
  }
}
