import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app_icon_manager.dart';

/// App Lifecycle Manager - Similar to iOS-devkhai AppDelegate
/// Handles app lifecycle events and triggers icon updates
class AppLifecycleManager extends WidgetsBindingObserver {
  static final AppLifecycleManager _instance = AppLifecycleManager._internal();
  static AppLifecycleManager get instance => _instance;

  AppLifecycleManager._internal();

  bool _isInitialized = false;

  /// Initialize the lifecycle manager
  /// Should be called in main() or app initialization
  void initialize() {
    if (_isInitialized) return;

    WidgetsBinding.instance.addObserver(this);
    _isInitialized = true;

    print('🔧 AppLifecycleManager initialized');

    // Trigger initial icon update (like app launch)
    _onAppBecameActive();
  }

  /// Dispose the lifecycle manager
  void dispose() {
    if (!_isInitialized) return;

    WidgetsBinding.instance.removeObserver(this);
    _isInitialized = false;

    print('🔧 AppLifecycleManager disposed');
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    print('🔄 App lifecycle state changed: $state');

    switch (state) {
      case AppLifecycleState.resumed:
        // App became active - exactly like iOS-devkhai applicationDidBecomeActive
        _onAppBecameActive();
        break;
      case AppLifecycleState.paused:
        // App went to background
        _onAppWentToBackground();
        break;
      case AppLifecycleState.inactive:
        // App became inactive (e.g., phone call, notification panel)
        break;
      case AppLifecycleState.detached:
        // App is detached
        break;
      case AppLifecycleState.hidden:
        // App is hidden (iOS 13+)
        break;
    }
  }

  /// Handle app became active - exactly like iOS-devkhai applicationDidBecomeActive
  void _onAppBecameActive() {
    print('📱 App became active - checking for Remote Config updates (respects user choice)');

    // Update app icon from Firebase Remote Config - but respect user's manual choices
    AppIconManager.updateAppIconFromRemoteConfig().catchError((error) {
      print('❌ Error updating app icon on app became active: $error');
    });
  }

  /// Handle app went to background
  void _onAppWentToBackground() {
    print('📱 App went to background');
    // Could add any background logic here if needed
  }

  /// Manually trigger icon update (for testing or manual refresh)
  static Future<void> triggerIconUpdate() async {
    print('🔄 Manually triggering icon update...');
    await AppIconManager.updateAppIconFromRemoteConfig();
  }

  /// Check if lifecycle manager is initialized
  bool get isInitialized => _isInitialized;
}
