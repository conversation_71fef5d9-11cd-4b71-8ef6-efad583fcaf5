# Firebase Remote Config Icon Setup Guide

## 🎯 **Overview**
Hệ thống tự động thay đổi icon app dựa trên Firebase Remote Config với key `icon_name`.

## 📱 **Icon Mapping**
```
Remote Config Value → Android Activity → Icon File
""                  → MainActivity     → ic_launcher
"Heart"             → MainActivityHeart → ic_launcher_heart  
"Mung_2_thang_9"    → MainActivityVn   → ic_launcher_vn
```

## 🔧 **Setup Firebase Remote Config**

### **1. Truy cập Firebase Console:**
1. Vào [Firebase Console](https://console.firebase.google.com/)
2. Chọn project Beta Cinema
3. Vào **Remote Config** từ menu bên trái

### **2. Tạo Parameter:**
```
Parameter key: icon_name
Default value: "" (empty string)
Description: Controls app icon based on value
```

### **3. Thêm Conditions (Optional):**
```
Condition Name: Special Events
Condition: app.version >= "1.0.0"
Value: "Heart" hoặc "Mung_2_thang_9"
```

### **4. Publish Changes:**
- Click **Publish changes** để áp dụng

## 🚀 **How It Works**

### **1. App Startup:**
```dart
// main.dart
final iconService = RemoteConfigIconService();
await iconService.initialize();
await iconService.fetchAndApplyIcon(); // Tự động áp dụng icon
```

### **2. Icon Service Logic:**
```dart
// Fetch từ Remote Config
final iconName = remoteConfig.getString('icon_name');

// Map to Android Activity
final className = {
  '': 'MainActivity',
  'Heart': 'MainActivityHeart', 
  'Mung_2_thang_9': 'MainActivityVn',
}[iconName];

// Apply icon change
await changeicon.switchIconTo(classNames: [className]);
```

### **3. Manual Testing:**
```dart
// Test buttons trong MovieSchedule
switchAppIcon('');              // Default icon
switchAppIcon('Heart');         // Heart icon
switchAppIcon('Mung_2_thang_9'); // Vietnam icon
refreshIconFromRemoteConfig();   // Refresh từ Remote Config
```

## 🎮 **Testing Guide**

### **1. Test Remote Config:**
1. Vào Firebase Console → Remote Config
2. Set `icon_name` = `"Heart"`
3. Publish changes
4. Trong app: Tap nút **Refresh** (màu cam)
5. Icon sẽ đổi thành heart

### **2. Test Manual Change:**
1. Tap nút **Default** (màu xanh) → ic_launcher
2. Tap nút **Heart** (màu đỏ) → ic_launcher_heart  
3. Tap nút **Vietnam** (màu xanh lá) → ic_launcher_vn

### **3. Test App Restart:**
1. Set Remote Config `icon_name` = `"Mung_2_thang_9"`
2. Force close app
3. Restart app
4. Icon sẽ tự động đổi thành Vietnam flag

## 📊 **Remote Config Values**

### **Default Icon:**
```json
{
  "icon_name": ""
}
```

### **Heart Icon (Valentine, Love events):**
```json
{
  "icon_name": "Heart"
}
```

### **Vietnam Icon (National holidays):**
```json
{
  "icon_name": "Mung_2_thang_9"
}
```

## 🔍 **Debug Information**

### **Console Logs:**
```
✅ RemoteConfigIconService initialized successfully
🔄 Fetching remote config for icon...
📱 Remote config icon_name: "Heart"
🎯 Applying icon change: "Heart" → MainActivityHeart
✅ Icon changed successfully to: MainActivityHeart
```

### **Debug Widget:**
```dart
// Thêm vào MaterialApp để xem debug info
RemoteConfigIconManager(
  showDebugInfo: true, // Show debug overlay
  child: YourApp(),
)
```

## ⚠️ **Important Notes**

### **1. Icon Files Required:**
Đảm bảo có đủ icon files trong `android/app/src/main/res/`:
```
mipmap-hdpi/ic_launcher.png
mipmap-hdpi/ic_launcher_heart.png  
mipmap-hdpi/ic_launcher_vn.png
... (all densities)
```

### **2. Android Manifest:**
Đảm bảo có đủ activity aliases trong `AndroidManifest.xml`:
```xml
<activity-alias
    android:name=".MainActivity"
    android:enabled="true"
    android:exported="true"
    android:icon="@mipmap/ic_launcher"
    android:targetActivity=".MainActivity">
</activity-alias>

<activity-alias
    android:name=".MainActivityHeart"
    android:enabled="false"
    android:exported="true"
    android:icon="@mipmap/ic_launcher_heart"
    android:targetActivity=".MainActivity">
</activity-alias>

<activity-alias
    android:name=".MainActivityVn"
    android:enabled="false"
    android:exported="true"
    android:icon="@mipmap/ic_launcher_vn"
    android:targetActivity=".MainActivity">
</activity-alias>
```

### **3. Fetch Interval:**
```dart
// Remote Config settings
minimumFetchInterval: Duration(hours: 1) // Fetch mỗi 1 giờ
fetchTimeout: Duration(minutes: 1)       // Timeout sau 1 phút
```

## 🎯 **Use Cases**

### **1. Seasonal Events:**
- **Tết Nguyên Đán:** `"Mung_2_thang_9"` → Vietnam flag
- **Valentine:** `"Heart"` → Heart icon
- **Normal days:** `""` → Default icon

### **2. Marketing Campaigns:**
- Set Remote Config theo campaign
- Icon tự động đổi cho tất cả users
- Không cần update app

### **3. A/B Testing:**
- Test icon nào tăng engagement
- Dùng Remote Config conditions
- Phân chia users theo segments

## 🚀 **Deployment**

### **1. Production:**
```
1. Set Remote Config trong Firebase Console
2. Publish changes
3. Users sẽ nhận icon mới trong 1 giờ (hoặc khi restart app)
```

### **2. Emergency Rollback:**
```
1. Set icon_name = "" trong Remote Config
2. Publish immediately
3. Icon sẽ về default cho tất cả users
```

**Hệ thống icon tự động đã sẵn sàng! 🎭✨**
