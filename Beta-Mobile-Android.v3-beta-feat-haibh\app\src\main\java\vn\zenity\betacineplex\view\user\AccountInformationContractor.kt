package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel
import vn.zenity.betacineplex.model.UserModel

/**
 * Created by Zenity.
 */

interface AccountInformationContractor {
    interface View : IBaseView {
        fun showUpdatedProfile(user: UserModel)
        fun showListCity(citis: ArrayList<CityModel>)
        fun showDistrict(cityId: String, districts: java.util.ArrayList<CityModel>)
        fun updateUserProfileSuccess(message: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun updateProfile(userId: String, profile: RegisterModel)
        fun getDistrict(cityID: String)
        fun getCity()
    }
}
