import 'package:flutter/material.dart';
import '../services/remote_config_icon_service.dart';

class RemoteConfigIconManager extends StatefulWidget {
  final Widget child;
  final bool showDebugInfo;

  const RemoteConfigIconManager({
    Key? key,
    required this.child,
    this.showDebugInfo = false,
  }) : super(key: key);

  @override
  State<RemoteConfigIconManager> createState() => _RemoteConfigIconManagerState();
}

class _RemoteConfigIconManagerState extends State<RemoteConfigIconManager> {
  final RemoteConfigIconService _iconService = RemoteConfigIconService();
  bool _isInitialized = false;
  bool _isLoading = true;
  String _currentIconName = '';
  String? _error;

  @override
  void initState() {
    super.initState();
    _initializeIconService();
  }

  Future<void> _initializeIconService() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      // Initialize the service
      await _iconService.initialize();

      // Fetch and apply icon from remote config
      await _iconService.fetchAndApplyIcon();

      // Get current icon name
      final iconName = _iconService.getCurrentIconName();

      if (mounted) {
        setState(() {
          _isInitialized = true;
          _isLoading = false;
          _currentIconName = iconName;
        });
      }

      print('✅ Icon service initialized successfully with icon: "$iconName"');

    } catch (e) {
      print('❌ Error initializing icon service: $e');
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  /// Refresh icon from remote config
  Future<void> _refreshIcon() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      await _iconService.fetchAndApplyIcon();
      final iconName = _iconService.getCurrentIconName();

      if (mounted) {
        setState(() {
          _isLoading = false;
          _currentIconName = iconName;
        });
      }

      print('✅ Icon refreshed successfully: "$iconName"');

    } catch (e) {
      print('❌ Error refreshing icon: $e');
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = e.toString();
        });
      }
    }
  }

  Widget _buildDebugInfo() {
    if (!widget.showDebugInfo) return const SizedBox.shrink();

    return Positioned(
      top: 100,
      right: 16,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: Colors.black87,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white24),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Icon Manager Debug',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 12,
              ),
            ),
            const SizedBox(height: 8),
            
            Text(
              'Status: ${_isLoading ? "Loading..." : _isInitialized ? "Ready" : "Error"}',
              style: TextStyle(
                color: _isLoading ? Colors.orange : _isInitialized ? Colors.green : Colors.red,
                fontSize: 10,
              ),
            ),
            
            Text(
              'Icon: "${_iconService.getIconDisplayName(_currentIconName)}"',
              style: const TextStyle(color: Colors.white70, fontSize: 10),
            ),
            
            if (_error != null)
              Text(
                'Error: $_error',
                style: const TextStyle(color: Colors.red, fontSize: 9),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            
            const SizedBox(height: 8),
            
            // Refresh button
            GestureDetector(
              onTap: _isLoading ? null : _refreshIcon,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _isLoading ? Colors.grey : Colors.blue,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  _isLoading ? 'Loading...' : 'Refresh',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        widget.child,
        _buildDebugInfo(),
      ],
    );
  }
}
