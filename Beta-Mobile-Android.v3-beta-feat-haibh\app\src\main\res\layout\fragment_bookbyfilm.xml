<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/grayBg"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar" />

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/book_by_film"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

    </LinearLayout>


    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/grayBg"
        android:paddingBottom="8dp">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/grayBg"
            android:orientation="vertical"
            app:elevation="0dp"
            app:expanded="true">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/grayBg"
                android:paddingBottom="10dp"
                app:layout_scrollFlags="scroll">

                <vn.zenity.betacineplex.helper.view.TopCropImageView
                    android:id="@+id/ivBanner"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/shape_bg_gray_gradient"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="@+id/ivBanner"
                    app:layout_constraintRight_toRightOf="@+id/ivBanner"
                    app:layout_constraintTop_toTopOf="@+id/ivBanner" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvFilmTitle"
                    style="@style/TextTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingLeft="@dimen/padding_small"
                    android:paddingTop="40dp"
                    android:paddingRight="@dimen/padding_small"
                    android:textColor="@color/black"
                    app:fontFamily="@font/oswald_bold"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="Beta Cineplex Mỹ Đình" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvFilmType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingLeft="@dimen/padding_small"
                    android:paddingRight="@dimen/padding_small"
                    android:textColor="@color/black"
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/sanspro_regular"
                    app:layout_constraintTop_toBottomOf="@+id/tvFilmTitle"
                    tools:text="2D - LT  |  Võ thuật, Viễn Tưởng  |  135 phút " />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/btnFilmDetail"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:background="@drawable/border_colorapp_rounded"
                    android:paddingLeft="20dp"
                    android:paddingTop="5dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="5dp"
                    android:text="@string/film_detail"
                    android:textColor="@color/colorPrimaryDark"
                    android:textSize="14sp"
                    app:fontFamily="@font/sanspro_bold"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvFilmType"
                    tools:visibility="gone" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recyclerDate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/grayBg"
                android:paddingBottom="@dimen/padding_small"
                app:layout_scrollFlags="enterAlways" />

            <LinearLayout
                android:id="@+id/llSelectArea"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="@dimen/padding_small"
                app:layout_scrollFlags="enterAlways">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/TextContent"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/select_area" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvTitleArea"
                    style="@style/TextTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/All"
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/sanspro_bold" />

                <androidx.appcompat.widget.AppCompatImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    app:srcCompat="@drawable/ic_arrownext" />
            </LinearLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <!--<com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout-->
        <!--android:id="@+id/refreshView"-->
        <!--android:layout_width="match_parent"-->
        <!--android:layout_height="match_parent"-->
        <!--app:layout_behavior="@string/appbar_scrolling_view_behavior"-->
        <!--app:tr_enable_loadmore="false">-->
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/recyclerView"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/grayBg"/>

                <LinearLayout
                    android:padding="4dp"
                    android:layout_marginBottom="8dp"
                    android:layout_gravity="center_horizontal"
                    android:id="@+id/btnLoadMore"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        app:srcCompat="@drawable/ic_arrowdown_blue" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="8dp"
                        android:text="@string/load_more"
                        android:textColor="@color/colorPrimary" />
                </LinearLayout>
            </LinearLayout>

        </ScrollView>


        <!--</com.lcodecore.tkrefreshlayout.TwinklingRefreshLayout>-->

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>
