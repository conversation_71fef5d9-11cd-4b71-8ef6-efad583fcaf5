package vn.zenity.betacineplex.view

import android.Manifest
import android.annotation.SuppressLint
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.AsyncTask
import android.os.Bundle
import android.os.Handler
import android.view.View
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.activity_home.*
import kotlinx.android.synthetic.main.app_bar_home.*
import kotlinx.android.synthetic.main.layout_menu.view.*
import org.jsoup.Jsoup
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.view.PopupFragment
import vn.zenity.betacineplex.helper.view.betamenu.BetaMenuItem
import vn.zenity.betacineplex.helper.view.betamenu.ResideMenu
import vn.zenity.betacineplex.model.AppParamsModel
import vn.zenity.betacineplex.model.NewNotification
import vn.zenity.betacineplex.view.auth.LoginFragment
import vn.zenity.betacineplex.view.cenima.CenimaDetailFragment
import vn.zenity.betacineplex.view.cenima.CenimaFragment
import vn.zenity.betacineplex.view.cenima.CinemaPriceFragment
import vn.zenity.betacineplex.view.event.EventDetailFragment
import vn.zenity.betacineplex.view.event.EventFragment
import vn.zenity.betacineplex.view.film.*
import vn.zenity.betacineplex.view.home.HomeFragment
import vn.zenity.betacineplex.view.notification.NotificationFragment
import vn.zenity.betacineplex.view.recruitment.RecruitmentFragment
import vn.zenity.betacineplex.view.setting.MoreFragment
import vn.zenity.betacineplex.view.setting.SettingFragment
import vn.zenity.betacineplex.view.user.BookHistoryDetailFragment
import vn.zenity.betacineplex.view.user.MemberFragment
import vn.zenity.betacineplex.view.user.VoucherFragment
import vn.zenity.betacineplex.view.user.point.BetaPointFragment
import vn.zenity.betacineplex.view.voucher.VoucherFreeDetailFragment
import java.util.*
import java.util.regex.Pattern

class HomeActivity : BaseActivity() {

    private var homeFragment: HomeFragment? = null
    var onlineVersion = ""
    private val handler = Handler()
    private var timeStarted = 0L
    private var timePaused = 0L
    private var timeRunned = 0L
    private var disposables: ArrayList<Disposable> = arrayListOf()
    private var isExpired = false
    var appParams: List<AppParamsModel> = listOf()
        set(value) {
            field = value
            field.firstOrNull { it.ParamsCode == Constant.AppParamCode.version }?.let {
                if (it.Value.isNotEmpty() && !PreferencesHelper.shared.getBooleanValue(it.Value, false)
                        && versionCompare(it.Value, BuildConfig.VERSION_NAME) > 0) {
                    PreferencesHelper.shared.putValue(it.Value, it.CanClose)
                    showConfirmWithHighlight(this@HomeActivity, it.ParamsMessage,
                            it.ParamsMessageHighLight,
                            leftButtonTitle = if (it.CanClose) R.string.cancel.getString() else null,
                            cancelable = it.CanClose,
                            rightButtonClickHandler = { dia ->
                                if (it.CanClose) {
                                    dia.dismiss()
                                }
                                openStore()
                            })
                }
            }
        }

    private val runnableTimeOver = Runnable {
        runOnUiThread {
            showConfirm(R.string.book.getString(), getString(R.string.time_for_booking_is_expried), "OK", handlerRight = {
                it.dismiss()
                backToHome()
            }, handleShow = {
                isExpired = true
            })
        }
    }

    fun startCountdown() {
        if (timeRunned <= 0L) {
            timeStarted = System.currentTimeMillis()
        } else {
            if (timePaused > 0) {
                timeStarted += (System.currentTimeMillis() - timePaused)
                timePaused = 0L
            }
        }
        handler.removeCallbacks(runnableTimeOver)
        if (BuildConfig.DEBUG) {
            handler.postDelayed(runnableTimeOver, 10 * 60 * 1000 - timeRunned)
        } else {
            handler.postDelayed(runnableTimeOver, 10 * 60 * 1000 - timeRunned)
        }
    }

    fun stopCountdown() {
        timeStarted = 0L
        timeRunned = 0L
        timePaused = 0L
        handler.removeCallbacks(runnableTimeOver)
    }

    fun pauseCountdown() {
        timePaused = System.currentTimeMillis()
        timeRunned = System.currentTimeMillis() - timeStarted
        handler.removeCallbacks(runnableTimeOver)
    }

    fun checkCountDown() {
        if (timeStarted > 0 && System.currentTimeMillis() - timeStarted >= Constant.orderTime && !isExpired) { //10 * 60 * 1000
            handler.post(runnableTimeOver)
        }
    }

    override fun contentFragment(): BaseFragment? {
        if (homeFragment == null) {
            homeFragment = HomeFragment()
        }
        return homeFragment
    }

    override fun isUsingBaseContent() = false
    private lateinit var resideMenu: ResideMenu
    private var currentMenu = "menu"

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setLightStatusBar()
        setContentView(R.layout.activity_home)
        GetVersionCode().execute()
        addContentFragmentIfEmpty()

        resideMenu = ResideMenu(this, -1, R.layout.layout_menu)
        resideMenu.setShadowVisible(false)
//        resideMenu.setScaleValue(0.7f)
        resideMenu.setBackgroundColor(R.color.grayBg.getColor())
        resideMenu.attachToActivity(this)
        resideMenu.setSwipeDirectionDisable(ResideMenu.DIRECTION_LEFT)
        resideMenu.setSwipeDirectionDisable(ResideMenu.DIRECTION_RIGHT)
        setupMenu()
        supportFragmentManager.addOnBackStackChangedListener {
            val index = supportFragmentManager.backStackEntryCount
            if (index == 0) return@addOnBackStackChangedListener
            val frag = supportFragmentManager.findFragmentById(R.id.contentLayout)
            var selected = R.string.menu_home.getString()
            when (frag) {
                is NotificationFragment -> selected = R.string.menu_notification.getString()
                is SettingFragment -> selected = R.string.menu_setting.getString()
                is EventFragment -> selected = R.string.menu_news.getString()
                is RecruitmentFragment -> selected = R.string.menu_recruitment.getString()
                is MemberFragment -> selected = R.string.menu_member_beta.getString()
                is LoginFragment -> selected = R.string.menu_member_beta.getString()
                is CenimaFragment -> selected = R.string.menu_beta_cinema.getString()
            }

            val bottomMenu = when (frag) {
                is HomeFragment -> R.string.menu_home.getString()
                is MoreFragment -> R.string.menu_setting.getString()
                is VoucherFragment -> R.string.menu_voucher.getString()
                is EventFragment -> R.string.menu_news.getString()
                is CenimaFragment -> {
                    if (frag.type == CenimaFragment.TYPE_DETAIL) {
                        R.string.menu_setting.getString()
                    } else {
                        R.string.menu_beta_cinema.getString()
                    }
                }

                else -> "menu"
            }

            currentMenu = bottomMenu
//            if ((frag as BaseFragment).isTransfStatus) {
//                settingTranfStatus()
//            } else {
            disableTranfStatus()
//            }
            changeMenuSelected(selected)
            changeBottomMenuSelected(bottomMenu)
            (frag as? BaseFragment)?.updateMenuNotifi(numberNotification, true)
        }
        changeBottomMenuSelected(R.string.menu_home.getString())
        numberNotification += 0

        intent?.let {
            if (intent.hasExtra("data")) {
                logD(intent.toString())
                processNotificationIntent(intent)
            }
        }
    }

    override fun onDestroy() {
        disposables.forEach {
            it.dispose()
        }
        super.onDestroy()
    }

    fun openMenu() {
//        if (!drawer_layout.isDrawerOpen(GravityCompat.START)) {
//            drawer_layout.openDrawer(GravityCompat.START)
//        }
        resideMenu.openMenu(ResideMenu.DIRECTION_RIGHT)
    }

    private fun setupMenu() {
        resideMenu.rightMenuView.listMenu?.forEach { view ->
            view.setOnClickListener { menuItem ->
                when ((menuItem as BetaMenuItem).menuTag) {
                    R.string.menu_home.getString() -> {
//                        openFragment(HomeFragment())
                        backToHome()
                    }

                    R.string.menu_member_beta.getString() -> {
                        if (Global.share().isLogin) {
                            openFragment(MemberFragment())
                        } else {
                            openFragment(LoginFragment())
                        }
                    }

                    R.string.menu_beta_cinema.getString() -> {
                        openFragment(CenimaFragment.getInstance(CenimaFragment.TYPE_BOOKING))
                    }

                    R.string.menu_news.getString() -> {
                        openFragment(EventFragment())
                    }

                    R.string.menu_recruitment.getString() -> {
                        openFragment(RecruitmentFragment())
                    }

                    R.string.menu_notification.getString() -> {
                        if (Global.share().isLogin) {
                            openFragment(NotificationFragment())
                        } else {
                            showMessage(getString(R.string.see_notification_warning))
                        }

                    }

                    R.string.menu_setting.getString() -> {
                        openFragment(SettingFragment())
                    }

                    R.string.ticket_price_menu.getString() -> {
                        PopupFragment.getInstance(CinemaPriceFragment()).showPopup(supportFragmentManager)
                    }
                }
                resideMenu.closeMenu()
            }
        }
        resideMenu.rightMenuView.menuBookByFilm?.setOnClickListener {
            openFragment(ListFilmBookFragment())
            resideMenu.closeMenu()
        }
        resideMenu.rightMenuView.menuBookByCinema?.setOnClickListener {
            openFragment(CenimaFragment.getInstance(CenimaFragment.TYPE_BOOKING))
            resideMenu.closeMenu()
        }
        resideMenu.rightMenuView.menuClose?.setOnClickListener {
            resideMenu.closeMenu()
        }
        llHomeCinema.setOnClickListener {
            if (currentMenu == getString(R.string.menu_beta_cinema)) return@setOnClickListener
            openFragment(CenimaFragment.getInstance(CenimaFragment.TYPE_BOOKING))
        }
        llHomeFilm.setOnClickListener {
            if (currentMenu == getString(R.string.menu_home)) return@setOnClickListener
            backToHome()
        }
        llHomeVoucher.setOnClickListener {
            if (currentMenu == getString(R.string.menu_voucher)) return@setOnClickListener
            if (Global.share().isLogin) {
                openFragment(VoucherFragment())
            } else {
                openFragment(LoginFragment())
            }
        }
        llHomeGift.setOnClickListener {
            if (currentMenu == getString(R.string.menu_news)) return@setOnClickListener
            openFragment(EventFragment())
        }
        llHomeMore.setOnClickListener {
            if (currentMenu == getString(R.string.menu_setting)) return@setOnClickListener
            openFragment(MoreFragment())
        }
    }

    private fun changeMenuSelected(type: String) {
        resideMenu.rightMenuView.listMenu?.forEach { menuItem ->
            (menuItem as BetaMenuItem).let {
                it.changeSelected(it.menuTag == type)
            }
        }
    }

    private fun changeBottomMenuSelected(menu: String) {
        when (menu) {
            getString(R.string.menu_home),
            getString(R.string.menu_setting) -> {
                setLightStatusBar()
            }

            else -> {
                clearLightStatusBar()
            }
        }
        llBottomBar.visible(menu != "menu")
        listOf<View>(tvHomeCinema, tvHomeFilm, tvHomeGift, tvHomeVoucher, tvHomeMore,
                ivHomeCinema, ivHomeFilm, ivHomeGift, ivHomeVoucher, ivHomeMore).forEach {
            it.isSelected = ((it.parent as? View)?.tag as? String) == menu
        }
    }

    override fun onBackPressed() {
        if (resideMenu.isOpened) {
            resideMenu.closeMenu()
        } else {
            val frag = supportFragmentManager.findFragmentById(R.id.contentLayout)
            if (frag is HomeFragment) {
                finish()
                return
            }
            if (frag is BookingPaymentFragment) {
                frag.back()
                return
            }
            if (frag is BookHistoryDetailFragment) {
                if (frag.isPaymentSuccess) {
                    backToHome()
                    return
                }
            }
            super.onBackPressed()
        }
    }

    fun backToHome(fragment: BaseFragment? = null) {
        var frag = supportFragmentManager.findFragmentById(R.id.contentLayout)
        while (frag !is HomeFragment) {
            supportFragmentManager.popBackStackImmediate()
            frag = supportFragmentManager.findFragmentById(R.id.contentLayout)
        }
        App.shared().updateTopInfo()
        fragment?.let {
            openFragment(it)
        }
        isExpired = false
    }

    fun call(phone: String) {
        if (ContextCompat.checkSelfPermission(this, Manifest.permission.CALL_PHONE)
                != PackageManager.PERMISSION_GRANTED) {
            // Permission is not granted
            // Should we show an explanation?
            if (ActivityCompat.shouldShowRequestPermissionRationale(this,
                            Manifest.permission.CALL_PHONE)) {
            } else {
                // No explanation needed, we can request the permission.
                ActivityCompat.requestPermissions(this,
                        arrayOf(Manifest.permission.CALL_PHONE),
                        11)
            }
        } else {
            val intent = Intent(Intent.ACTION_CALL, Uri.parse("tel:$phone"))
            startActivity(intent)
        }
    }

    override fun onResume() {
        super.onResume()
        updateNotification()
        checkCountDown()
    }

    var numberNotification = 0
        set(value) {
            field = value
            runOnUiThread {
                if (field > 0) {
                    resideMenu.rightMenuView?.listMenu?.notifiMenu?.changeIcon(R.drawable.ic_notification_with_dot)
                } else {
                    resideMenu.rightMenuView?.listMenu?.notifiMenu?.changeIcon(R.drawable.ic_notification)
                }
                val frag = supportFragmentManager.findFragmentById(R.id.contentLayout)
                (frag as? BaseFragment)?.updateMenuNotifi(field)
            }
        }

    fun updateNotification() {
//        val lang = App.shared().getCurrentLang()
//        APIClient.shared.accountAPI.getNumberNotificationUnread(if (lang == "en") lang else "", dataTime = Date().toStringFormat(Constant.DateFormat.requestServer)
//                ?: return)
//                .applyOn()
//                .subscribe({
//                    numberNotification = if (it.isSuccess) {
//                        it.Data?.get("NumberUnread") ?: 0
//                    } else {
//                        0
//                    }
//                }, {
//                    numberNotification = 0
//                })
        if (Global.share().user?.AccountId != null) {
            Global.share().user?.AccountId?.let {
                APIClient.shared.ecmAPI.getNumberUnread(it).applyOn()
                        .subscribe({
                            numberNotification = it.Data?.TotalUnRead ?: 0
                        }, {
                            numberNotification = 0
                        })
            }
        } else {
            numberNotification = 0
        }
    }

    fun openStore() {
        val appPackageName = packageName // getPackageName() from Context or Activity object
        try {
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=$appPackageName")))
        } catch (_: android.content.ActivityNotFoundException) {
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse("https://play.google.com/store/apps/details?id=$appPackageName")))
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let {
            if (intent.hasExtra("data")) {
                logD(intent.toString())
                processNotificationIntent(intent)
            }
            if (intent.scheme != null && intent.scheme?.startsWith(Constant.Momo.callbackScheme) == true) {
                if (intent.data?.host?.contains(Constant.Momo.host) == true) {
                    val resultCode = intent.data?.getQueryParameters(Constant.Momo.resultCode)
                            ?: listOf()
                    val requestId = intent.data?.getQueryParameters(Constant.Momo.requestId)
                            ?: listOf()
                    val transId = intent.data?.getQueryParameters(Constant.Momo.transId) ?: listOf()
                    val message = intent.data?.getQueryParameters(Constant.Momo.message) ?: listOf()
                    val responseTime = intent.data?.getQueryParameters(Constant.Momo.responseTime)
                            ?: listOf()
                    val payType = intent.data?.getQueryParameters(Constant.Momo.payType) ?: listOf()
                    val extraData = intent.data?.getQueryParameters(Constant.Momo.extraData)
                            ?: listOf("")
                    val orderId = intent.data?.getQueryParameters(Constant.Momo.orderId) ?: listOf()
                    val partnerCode = intent.data?.getQueryParameters(Constant.Momo.partnerCode)
                            ?: listOf()
                    val newIntent = Intent(Constant.INTENT_FILTER_MOMO)
                    if (resultCode.isNotEmpty() && orderId.isNotEmpty()
                    ) {
                        newIntent.putExtra(Constant.Momo.orderId, orderId.first())
                        newIntent.putExtra(Constant.Momo.resultCode, resultCode.first())
                        newIntent.putExtra(Constant.Momo.requestId, requestId.first())
                        newIntent.putExtra(Constant.Momo.transId, transId.first())
                        newIntent.putExtra(Constant.Momo.message, message.first())
                        newIntent.putExtra(Constant.Momo.responseTime, responseTime.first())
                        newIntent.putExtra(Constant.Momo.payType, payType.first())
                        newIntent.putExtra(Constant.Momo.extraData, extraData.first())
                        newIntent.putExtra(Constant.Momo.partnerCode, partnerCode.first())
                    }
                    sendBroadcast(newIntent)
                    return
                }

                if (intent.data?.host?.contains(Constant.ZaloPay.domain) == true) {
                    val appId = intent.data?.getQueryParameters(Constant.ZaloPay.appId) ?: listOf()
                    val appTransId = intent.data?.getQueryParameters(Constant.ZaloPay.appTransId)
                            ?: listOf()
                    val pmcId = intent.data?.getQueryParameters(Constant.ZaloPay.pmcId)
                            ?: listOf()
                    val bankCode = intent.data?.getQueryParameters(Constant.ZaloPay.bankCode)
                            ?: listOf()
                    val amount = intent.data?.getQueryParameters(Constant.ZaloPay.amount)
                            ?: listOf()
                    val dAmount = intent.data?.getQueryParameters(Constant.ZaloPay.dAmount)
                            ?: listOf()
                    val appStatus = intent.data?.getQueryParameters(Constant.ZaloPay.appStatus)
                            ?: listOf()
                    val checkSum = intent.data?.getQueryParameters(Constant.ZaloPay.checkSum)
                            ?: listOf()

                    val newIntent = Intent(Constant.INTENT_FILTER_ZALOPAY)
                    if (appId.isNotEmpty() && appTransId.isNotEmpty()
                    ) {
                        newIntent.putExtra(Constant.ZaloPay.appId, appId.first())
                        newIntent.putExtra(Constant.ZaloPay.appTransId, appTransId.first())
                        newIntent.putExtra(Constant.ZaloPay.pmcId, pmcId.first())
                        newIntent.putExtra(Constant.ZaloPay.bankCode, bankCode.first())
                        newIntent.putExtra(Constant.ZaloPay.amount, amount.first())
                        newIntent.putExtra(Constant.ZaloPay.dAmount, dAmount.first())
                        newIntent.putExtra(Constant.ZaloPay.appStatus, appStatus.first())
                        newIntent.putExtra(Constant.ZaloPay.checkSum, checkSum.first())
                    }
                    sendBroadcast(newIntent)
                    return
                }
            }
        }
    }

    private fun processNotificationIntent(intent: Intent) {
        val data = intent.getStringExtra("data")
        val mapData = Gson().fromJson<Map<String, Any>>(data, object : TypeToken<Map<String, Any>>() {}.type)
        val code = (mapData["ScreenCode"] ?: mapData["SreenCode"]) as? String ?: ""
        val id = (mapData["Id"] as? Int) ?: 0
        if (code.isNotEmpty()) {
            val refId = mapData["RefId"] as? String ?: ""
            provideAppLink(code to refId, id)
        } else if (mapData.containsKey("id")) {
            drawer_layout.postDelayed({
                val id = (mapData["id"] as? Int) ?: ((mapData["id"] as? String)?.toInt() ?: 0)
                if (id > 0) {
                    val notify = NewNotification()
                    notify.NotificationCampaignId = id
                    openFragment(EventDetailFragment.getInstance(null, notify, R.string.notification.getString()))
                }
            }, 1500)
        }
        <EMAIL> = null
    }

    private fun provideAppLink(it: Pair<String, String>, id: Int) {
        when (it.first) {
            "1" -> {
                val filmId = it.second
                openFragment(BookByFilmFragment.getInstance(filmId))
            }

            "2" -> {
                val cinemaId = it.second
                openFragment(CenimaDetailFragment.getInstance(cinemaId))
            }

            "3", "6" -> {
                val eventId = it.second
                openFragment(EventDetailFragment.getInstance(null, eventId = eventId))
            }

            "4" -> {
                val filmId = it.second
                openFragment(FilmDetailFragment.getInstance(filmId, true))
            }

            "5" -> {
                val cinemaId = it.second
                openFragment(BookByCinemaFragment.getInstance(null, cinemaId))
            }

            "7", "11" -> {
                val voucherId = it.second
                openFragment(VoucherFreeDetailFragment.getInstance(voucherId))
            }

            "8" -> {
                val voucherId = it.second
                openFragment(BookHistoryDetailFragment.getInstance(voucherId))
            }

            "9" -> {
                if (Global.share().isLogin) {
                    openFragment(VoucherFragment())
                } else {
                    openFragment(LoginFragment())
                }
            }

            "10" -> {
                openFragment(BetaPointFragment())
            }
        }
        APIClient.shared.ecmAPI.readNotificationV2(mapOf("id" to "$id", "ScreenCode" to it.first))
                .applyOn().subscribe()
    }

    fun getAppParamsViaApi() {
        val dis = APIClient.shared.accountAPI.getAppParams()
                .applyOn()
                .subscribe({
                    this.appParams = it.Data ?: listOf()
                }, {
                })
        disposables.add(dis)
    }

    @SuppressLint("StaticFieldLeak")
    private inner class GetVersionCode : AsyncTask<Void, String, String>() {
        override fun doInBackground(vararg voids: Void): String {

            var newVersion = ""
            return try {
                val document = Jsoup.connect("https://play.google.com/store/apps/details?id=" + <EMAIL> + "&hl=en")
                        .timeout(30000)
                        .userAgent("Mozilla/5.0 (Windows; U; WindowsNT 5.1; en-US; rv1.8.1.6) Gecko/******** Firefox/2.0.0.6")
                        .referrer("http://www.google.com")
                        .get()
                var elements = document
                        .select("div[itemprop=softwareVersion]")
                if (elements.size <= 0) {
                    elements = document
                            .select(".htlgb")
                    if (elements.size <= 0) {
                        return ""
                    }
                }
                for (el in elements) {
                    if (el.childNodeSize() > 0) {
                        val data = el.childNode(0).outerHtml()
                        if (Pattern.matches("^[0-9].[0-9].[0-9]$", data)) {
                            return data
                        }
                    }
                }
                newVersion = elements.first()
                        .ownText()

                newVersion
            } catch (e: Exception) {
                newVersion
            }

        }

        override fun onPostExecute(onlineVersion: String?) {
            super.onPostExecute(onlineVersion)
            if (onlineVersion != null && !onlineVersion.isEmpty()) {
                <EMAIL> = onlineVersion
            }
        }
    }
}
