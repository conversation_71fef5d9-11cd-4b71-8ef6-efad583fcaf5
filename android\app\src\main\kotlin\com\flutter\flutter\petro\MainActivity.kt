package com.flutter.flutter.petro

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.annotation.NonNull
import com.betacineplex.SignalRPlugin
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.plugin.common.MethodChannel

class MainActivity: FlutterActivity() {
       // 🚫 onActivityResult removed - no longer needed without PaymentWebViewActivity
}
