import 'package:flutter/material.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_svg/flutter_svg.dart';

class SeatGrid extends StatefulWidget {
  const SeatGrid({
    super.key,
    required this.seatPositions,
    required this.selectedSeats,
    required this.onSeatSelected,
    required this.onSeatDeselected,
  });

  final List<List<SeatModel>> seatPositions;
  final List<SeatModel> selectedSeats;
  final Function(SeatModel) onSeatSelected;
  final Function(SeatModel) onSeatDeselected;

  @override
  State<SeatGrid> createState() => _SeatGridState();
}

class _SeatGridState extends State<SeatGrid> {


  late List<List<SeatModel>> processedSeatPositions;

  @override
  void initState() {
    super.initState();
    processedSeatPositions = _preprocessCoupleSeatPairing(widget.seatPositions);
  }

  @override
  void didUpdateWidget(covariant SeatGrid oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.seatPositions != widget.seatPositions) {
      processedSeatPositions = _preprocessCoupleSeatPairing(widget.seatPositions);
    }
  }

  @override
  Widget build(BuildContext context) {
    // ✅ Pre-process couple seats before rendering
    // Use preprocessed seats from state
    final processedSeatPosition = processedSeatPositions;

    // Calculate the maximum number of seats in a row
    int maxSeatsInRow = 0;
    for (var row in processedSeatPosition) {
      final Set<int> countedSeatIndices = {};
      int seatsCount = 0;

      for (var seat in row) {
        if (seat.seatIndex != null && countedSeatIndices.contains(seat.seatIndex!)) {
          continue;
        }

        if (seat.seatTypeEnum == SeatType.COUPLE) {
          seatsCount += 2;
          if (seat.seatIndex != null) countedSeatIndices.add(seat.seatIndex!);
          if (seat.coupleSeat?.seatIndex != null) countedSeatIndices.add(seat.coupleSeat!.seatIndex!);
        } else if (!seat.isWay && !seat.isBroken) {
          seatsCount += 1;
        }
      }
      maxSeatsInRow = seatsCount > maxSeatsInRow ? seatsCount : maxSeatsInRow;
    }

    const double sizeMultiplier = 0.8;
    final effectiveSeatsPerRow = (maxSeatsInRow * sizeMultiplier).ceil();

    return LayoutBuilder(
      builder: (context, constraints) {
        final availableWidth = constraints.maxWidth;
        final seatSize = (availableWidth / effectiveSeatsPerRow).floorToDouble();

        return SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 5),
                for (int col = 0; col < processedSeatPosition.length; col++)
                  _buildRow(col, seatSize, processedSeatPosition),
                const SizedBox(height: 5),
              ],
            ),
          ),
        );
      },
    );
  }

  /// ✅ NEW METHOD: Pre-process couple seat pairing based on seat numbers
  List<List<SeatModel>> _preprocessCoupleSeatPairing(List<List<SeatModel>> originalSeatPositions) {
    final processedPositions = <List<SeatModel>>[];

    for (var row in originalSeatPositions) {
      final processedRow = <SeatModel>[];
      final Set<int> pairedSeatIndices = {};

      for (var seat in row) {
        // Skip if this seat has already been paired
        if (seat.seatIndex != null && pairedSeatIndices.contains(seat.seatIndex!)) {
          continue;
        }

        // If this is a couple seat, find its actual pair
        if (seat.seatTypeEnum == SeatType.COUPLE) {
          final couplePair = _findCouplePair(seat, row);

          if (couplePair != null &&
              !couplePair.isBroken &&
              !couplePair.isWay &&
              couplePair.seatIndex != null) {
            // Both seats are valid - create the couple pair

            // ✅ SYNC STATUS: If either seat is SOLD/SOLDED, both should be SOLD
            // This matches iOS behavior: seat?.coupleSeat?.SoldStatus = status
            SeatSoldStatus finalStatus = seat.soldStatus ?? SeatSoldStatus.EMPTY;
            if (couplePair.soldStatus == SeatSoldStatus.SOLD ||
                couplePair.soldStatus == SeatSoldStatus.SOLDED ||
                seat.soldStatus == SeatSoldStatus.SOLD ||
                seat.soldStatus == SeatSoldStatus.SOLDED) {
              finalStatus = SeatSoldStatus.SOLD;
            }
            // If either is BOOKED, both should be BOOKED
            else if (couplePair.soldStatus == SeatSoldStatus.BOOKED ||
                     seat.soldStatus == SeatSoldStatus.BOOKED) {
              finalStatus = SeatSoldStatus.BOOKED;
            }
            // If either is SELECTING/SELECTED, both should be SELECTING
            else if (couplePair.soldStatus == SeatSoldStatus.SELECTING ||
                     couplePair.soldStatus == SeatSoldStatus.SELECTED ||
                     seat.soldStatus == SeatSoldStatus.SELECTING ||
                     seat.soldStatus == SeatSoldStatus.SELECTED) {
              finalStatus = SeatSoldStatus.SELECTING;
            }

            // Create synced couple pair
            final syncedCouplePair = couplePair.copyWith(soldStatus: finalStatus);
            final pairedSeat = seat.copyWith(
              coupleSeat: syncedCouplePair,
              soldStatus: finalStatus,
            );

            // ✅ DEBUG: Log pairing details
            print('🎭 Pairing: ${seat.seatNumber} (${seat.soldStatus?.toInt()}) with ${couplePair.seatNumber} (${couplePair.soldStatus?.toInt()}) → Final: ${finalStatus.toInt()}');

            // Mark both as paired
            pairedSeatIndices.add(seat.seatIndex!);
            pairedSeatIndices.add(couplePair.seatIndex!);

            // Add the main seat (the pair will be skipped in rendering)
            processedRow.add(pairedSeat);
          } else {
            // Couple pair is broken/missing - don't show this couple seat
            if (seat.seatNumber != null && RegExp(r'^[A-Z]\d+$').hasMatch(seat.seatNumber!)) {
              print('⚠️ Couple seat ${seat.seatNumber} has broken/missing pair - hiding');
            }
            continue;
          }
        } else {
          // Regular seat - add as is
          processedRow.add(seat);
        }
      }

      processedPositions.add(processedRow);
    }

    return processedPositions;
  }

  /// ✅ CORRECTED: Find the actual couple pair for a seat
  SeatModel? _findCouplePair(SeatModel seat, List<SeatModel> row) {
    if (seat.seatNumber == null || seat.seatNumber!.isEmpty) return null;

    final seatName = seat.seatNumber!;

    // Skip non-standard seat names (like "Lối vào", "Exit", etc.)
    if (!RegExp(r'^[A-Z]\d+$').hasMatch(seatName)) {
      return null; // Don't print warning for non-standard seats
    }

    // Extract row letter and number - Example: "J1" → row="J", number=1
    final rowLetter = seatName.substring(0, 1);
    final seatNumberStr = seatName.substring(1);
    final seatNumber = int.tryParse(seatNumberStr);

    if (seatNumber == null) return null;

    // ✅ COUPLE PAIRING LOGIC: Only odd numbers look for their even pair
    // This prevents double processing (J1 looks for J2, but J2 doesn't look for J1)
    String pairSeatName;
    if (seatNumber % 2 == 1) {
      // Odd seat (J1, J3, J5) pairs with next even seat (J2, J4, J6)
      pairSeatName = '$rowLetter${seatNumber + 1}';
    } else {
      // Even seats don't initiate pairing - they get paired by their odd counterpart
      return null;
    }

    // Find the pair seat in the same row
    final pairSeats = row.where((s) =>
    s.seatNumber == pairSeatName &&
        s.seatTypeEnum == SeatType.COUPLE &&
        !s.isBroken &&
        !s.isWay
    ).toList();

    final pairSeat = pairSeats.isNotEmpty ? pairSeats.first : null;

    // Only print warning if we expected to find a pair but couldn't
    if (pairSeat == null) {
      print('⚠️ Could not find valid couple pair for ${seat.seatNumber}, looking for $pairSeatName');
    }

    return pairSeat;
  }

  Widget _buildRow(int col, double seatSize, List<List<SeatModel>> processedSeatPositions) {
    final Set<int> renderedSeatIndices = {};

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 1),
      child: FittedBox(
        fit: BoxFit.scaleDown,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            for (int row = 0; row < processedSeatPositions[col].length; row++)
              _buildSeatWithTracking(processedSeatPositions[col][row], seatSize, renderedSeatIndices),
          ],
        ),
      ),
    );
  }

  /// ✅ SIMPLIFIED: No more complex pairing logic needed here
  Widget _buildSeatWithTracking(SeatModel seat, double baseSeatSize, Set<int> renderedSeatIndices) {
    // Skip if this seat has already been rendered as part of a couple seat
    if (seat.seatIndex != null && renderedSeatIndices.contains(seat.seatIndex!)) {
      return const SizedBox.shrink();
    }

    // For couple seats, mark both seats as rendered
    if (seat.seatTypeEnum == SeatType.COUPLE && seat.coupleSeat != null) {
      if (seat.seatIndex != null) renderedSeatIndices.add(seat.seatIndex!);
      if (seat.coupleSeat!.seatIndex != null) {
        renderedSeatIndices.add(seat.coupleSeat!.seatIndex!);
      }
    }

    return _buildSeat(seat, baseSeatSize);
  }

  Widget _buildSeat(SeatModel seat, double baseSeatSize) {
    // Skip if it's a way or not a seat
    if (seat.isWay || seat.isBroken || seat.isNotUsed || seat.isEntranceExit) {
      return SizedBox(width: baseSeatSize, height: baseSeatSize);
    }

    // Determine if this seat is selected
    final isSelected = widget.selectedSeats.any((s) =>
    s.seatNumber == seat.seatNumber ||
        (seat.coupleSeat != null && s.seatNumber == seat.coupleSeat!.seatNumber));

    // Get seat image based on type and status
    String seatImage = _getSeatImage(seat, isSelected);

    // Determine seat width based on type
    double seatWidth = seat.seatTypeEnum == SeatType.COUPLE ? baseSeatSize * 1.8 : baseSeatSize;
    double seatHeight = baseSeatSize * 1.1;

    return GestureDetector(
      onTap: () {
        if (seat.soldStatus == SeatSoldStatus.EMPTY && !isSelected) {
          widget.onSeatSelected(seat);
        } else if (isSelected) {
          widget.onSeatDeselected(seat);
        }
      },
      child: Container(
        margin: EdgeInsets.all(baseSeatSize * 0.04),
        child: Stack(
          alignment: Alignment.center,
          children: [
            SvgPicture.asset(
              seatImage,
              width: seatWidth,
              height: seatHeight,
              fit: BoxFit.contain,
            ),
            Text(
              _getSeatLabel(seat),
              style: TextStyle(
                fontSize: baseSeatSize * 0.4,
                fontWeight: FontWeight.w600,
                color: isSelected ? Colors.white : (seat.soldStatus != SeatSoldStatus.EMPTY) ? Colors.white : Colors.black,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _getSeatImage(SeatModel seat, bool isSelected) {
    // ✅ DEBUG: Log seat status for debugging
    if (seat.seatNumber?.startsWith('J') == true) {
      print('🎭 _getSeatImage: ${seat.seatNumber} - SoldStatus=${seat.soldStatus?.toInt()}, isSelected=$isSelected');
    }

    const String basePath = 'assets/icon/cinema';

    const String normalEmpty = '$basePath/ic_empty_normal_seat.svg';
    const String vipEmpty = '$basePath/ic_empty_vip_seat.svg';
    const String coupleEmpty = '$basePath/ic_empty_couple_seat.svg';

    const String normalSelected = '$basePath/ic_select_normal_seat.svg';
    const String vipSelected = '$basePath/ic_select_vip_seat.svg';
    const String coupleSelected = '$basePath/ic_select_couple_seat.svg';

    const String normalSelecting = '$basePath/ic_process_normal_seat.svg';
    const String vipSelecting = '$basePath/ic_process_vip_seat.svg';
    const String coupleSelecting = '$basePath/ic_process_couple_seat.svg';

    const String normalBooked = '$basePath/ic_set_normal_seat.svg';
    const String vipBooked = '$basePath/ic_set_vip_seat.svg';
    const String coupleBooked = '$basePath/ic_set_couple_seat.svg';

    const String normalSoldFinal = '$basePath/ic_sold_normal_seat.svg';
    const String vipSoldFinal = '$basePath/ic_sold_vip_seat.svg';
    const String coupleSoldFinal = '$basePath/ic_sold_couple_seat.svg';

    if (isSelected) {
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalSelected;
      if (seat.seatTypeEnum == SeatType.VIP) return vipSelected;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleSelected;
    }
    else if (seat.soldStatus == SeatSoldStatus.EMPTY) {
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalEmpty;
      if (seat.seatTypeEnum == SeatType.VIP) return vipEmpty;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleEmpty;
    }
    else if (seat.soldStatus == SeatSoldStatus.SELECTING ||
        seat.soldStatus == SeatSoldStatus.SELECTED||
        seat.soldStatus == SeatSoldStatus.WAITING) {
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalSelecting;
      if (seat.seatTypeEnum == SeatType.VIP) return vipSelecting;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleSelecting;
    }  else if (seat.soldStatus == SeatSoldStatus.BOOKED) {
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalBooked;
      if (seat.seatTypeEnum == SeatType.VIP) return vipBooked;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleBooked;
    }
    else {
      if (seat.seatTypeEnum == SeatType.NORMAL) return normalSoldFinal;
      if (seat.seatTypeEnum == SeatType.VIP) return vipSoldFinal;
      if (seat.seatTypeEnum == SeatType.COUPLE) return coupleSoldFinal;
    }

    return normalEmpty;
  }

  /// ✅ FINAL: Show full seat names for couple seats
  String _getSeatLabel(SeatModel seat) {
    if (seat.seatNumber == null || seat.seatNumber!.isEmpty) return '';

    // For couple seats, show both seat numbers if available
    if (seat.seatTypeEnum == SeatType.COUPLE) {
      final String mainSeatNumber = seat.seatNumber!;
      final int mainSeatIndex = seat.seatIndex!;

      // If we have a linked couple seat, show both full names
      if (seat.coupleSeat != null && seat.coupleSeat!.seatNumber != null) {
        final String coupleSeatNumber = seat.coupleSeat!.seatNumber!;
        final int coupleSeatIndex = seat.coupleSeat!.seatIndex!;

        // Show full seat names like "J1 J2" or "J3 J4"
        final List<String> seatNames = [mainSeatNumber, coupleSeatNumber];

        // Sort by seat number to ensure consistent display (J1 J2, not J2 J1)
        seatNames.sort((a, b) {
          final numA = mainSeatIndex;
          final numB = coupleSeatIndex;
          return numA.compareTo(numB);
        });

        return seatNames.join(' '); // "J1 J2"
      }

      // Fallback: show just the main seat number
      return mainSeatNumber;
    }

    // For regular seats, show the full seat number (including row letter)
    return seat.seatNumber!;
  }

  /// ✅ HELPER: Extract number from seat name (J1 → 1)
  int? _extractSeatNumber(String seatName) {
    if (seatName.length <= 1) return null;
    return int.tryParse(seatName.substring(1));
  }
}
